# 第一部分 库
from urllib.parse import quote  # 登录时,把中文账号转码使用
import requests                 # 请求通用,网络
import time                     # 全局通用,时间
import re                       # 全局通用,正则
import keyboard                 # 全局通用,键盘
import json                     # 全局通用,json
# 第二部分 自写代码
from mythread import MyThread, ThreadState   # 系统级线程,全局使用
from default import default_speed, default_reconnect, daily_tb_orders, daily_mh_orders   # 内置默认频率,单位秒
from proxy import get_proxy         # 获取代理
from yzm import get_yzm             # 获取验证码
from js_parser import parse_js_code, ret_packages # 解析js代码
from map import ret_way


class MYJ:
    def __init__(self, config):
        # 内置默认配置
        self.延迟 = default_speed  # 内置默认频率,单位秒
        self.服务器 = config['服务器']
        self.账号 = config['账号']
        self.密码 = config['密码']
        self.角色序号 = config['角色序号']
        self.代理配置 = config.get('代理配置', None)
        self.proxies = None        
        self.cookies = None
        self.petId = None
        self.myUserId = None
        self.validateParam = None
        self.patterns = {
            'npc': re.compile(r"p\._getNpc\(([^)]*)\)"),
            'modify': re.compile(r'<[A-Za-z0-9 ="\':;/_\-.]*>'),
            'petLv': re.compile(r"petLv=(\d+);"),   # 宠物等级
            'nowMap': re.compile(r'nowMap\s*=\s*"([^"]+)"'),    # 当前所在地图
            'room': re.compile(r'room\s*=\s*"([^"]+)"'),    # 当前所在房间
            'hp_left': re.compile(r"<span id='hpLine_left_no'[^>]*>(\d+)</span>"),  # 剩余hp
            'hp_max': re.compile(r"<span id='hpLine_left_max_no'[^>]*>(\d+)</span>"),   # 最大hp
            'sp_left': re.compile(r"<span id='mpLine_left_no'[^>]*>(\d+)</span>"),  # 剩余sp
            'sp_max': re.compile(r"<span id='mpLine_left_max_no'>(\d+)</span>"),    # 最大sp
            'exp': re.compile(r'<\s*span\s+id\s*=\s*["\']?expBai["\']?\s*>(\d+)\s*</\s*span\s*>', re.IGNORECASE),   # 百分比经验
            'join_role': re.compile(r"selectimg\(\s*(\d+)\s*,\s*'click'\s*,\s*(\d+)[^)]*\)\s*.*?"
                                    r'<div\s+align="center"\s+style="position:relative;top:-10px">(.*?)</div>', re.DOTALL),  # 获取角色代码及序号部分
            # 'skills': re.compile(r"p\.cutArray\[(?:[0-5])\]='(perform.*?)';"),  #
            'petName': re.compile(r'petName\s?=\s?"(.*)";'),    # 宠物名
            'myUserId': re.compile(r'myUserId\s?=\s?"(.*)";'),  # id
            'validateParam': re.compile(r'validateParam\s?=\s?"(.*)";'),    # 链接验证参数
            'combat_start': re.compile(r"p\._combat\s*\(", re.IGNORECASE),      # 进入战斗
            'combat_end': re.compile(r"p\.lost\(\s*p\.petWin\.fighter_2\s*\)", re.IGNORECASE),  # 退出战斗
        }
        self.online_url = None
        self.online_thread = None
        self.战斗状态 = False
        self.Npc列表 = []
        self.房间 = None
        self.地图 = None
        self.重连间隔 = default_reconnect
        self.背包道具 = []
        self.背包道具更新时间 = None
        self.职业技能 = None
        self.技能列表 = []
        self.技能列表更新时间 = None
        self.等级 = None
        self.宠物名 = None
        self.hp_left = None
        self.sp_left = None
        self.hp_max = None
        self.sp_max = None
        self.经验百分比 = None
        self.最新命令 = None
        self.Npc对话框内容 = None
        self.命令地址 = f'http://{self.服务器}.pet.imop.com/action.jsp?'
        self.日常拖把开关 = config.get('日常拖把开关', True)
        self.日常礼包开关 = config.get('日常礼包开关', True)
        self.日常竞技场开关 = config.get('日常竞技场开关', False)
        self.日常魔化开关 = config.get('日常魔化开关', False)
        self.日常如意开关 = config.get('日常如意开关', False)
        self.日常乐园开关 = config.get('日常乐园开关', False)
        self.日常银月开关 = config.get('日常银月开关', False)
        self.日常还猪开关 = config.get('日常还猪开关', False)
        self.日常沼泽开关 = config.get('日常沼泽开关', False)
        self.日常小镇开关 = config.get('日常小镇开关', False)
        self.闲时任务 = config.get('闲时任务', None)

        # 任务调度相关属性
        self.任务线程 = None
        self.当前任务类型 = None  # 'daily', 'idle', 'activity'
        self.当前任务名称 = None
        self.上次日常执行日期 = {}  # 记录各日常任务的上次执行日期
        self.活动任务配置 = config.get('活动任务配置', {})  # 活动任务的时间配置
        self.调度器线程 = None

        self.pull_online()  # 初始化完了以后直接保持在线
    
    def 确认服务器登录状态(self):
        while True:
            print('获取代理配置')
            self.proxies = get_proxy(self.代理配置.get('代理账号'), self.代理配置.get('代理密码')) if self.代理配置 else None
            if self.proxies is not None:
                print('获取代理配置成功')
                _temp_frequency = 30
                _temp_sleep = 0.5
            else :
                print('获取代理配置失败')
                _temp_frequency = 30
                _temp_sleep = 1.51
            for i in range(_temp_frequency):
                res = requests.get(f'http://{self.服务器}.pet.imop.com')
                if res.status_code == 503:
                    print('维护未完成')
                    time.slee(_temp_sleep)
                else:
                    print(res.status_code)
                    print('维护完成')
                    return True

    def login(self):
        # 登录错误码
        _error_code = {
            '0': "登录失败,请重新登录",
            '1': "签名验证失败",
            '2': "时间戳过期",
            '3': "参数为空或格式不正确",
            '4': "用户名密码验证未通过",
            '5': "用户已被锁定",
            '6': "密保未通过",
            '7': "cookie验证未通过",
            '8': "token验证未通过",
            '9': "大区验证未通过",
            '11': "验证码错误",
            '12': "验证码为空",
            '999': "系统异常，登录失败"
        }
        # 登录信息
        self.确认服务器登录状态()
        while True:
            # 登录发送信息
            self.proxies = get_proxy(self.代理配置.get('代理账号'), self.代理配置.get('代理密码')) if self.代理配置 else None
            login_post = {
                'url': f'http://{self.服务器}.pet.imop.com/LoginAction.jsp',
                'cookies': {'mopet_logon': '123'},
                'headers': {
                    'Content-Type': 'application/x-www-form-urlencoded',
                    'Origin': f'http://{self.服务器}.pet.imop.com',
                    'Referer': f'http://{self.服务器}.pet.imop.com/login.html',
                    'Connection': 'keep-alive',
                    'User-Agent': 'Mozilla/4.0 (compatible; MSIE 7.0; Windows NT 10.0; WOW64; Trident/7.0; .NET4.0C; .NET4.0E)',
                },
                'data': {
                    'user_name': quote(self.账号, encoding="gbk"),
                    'password': self.密码,
                    'checkcode': get_yzm(proxies=self.proxies)
                },
                'proxies': self.proxies,
            }
            # 登录返回信息
            login_response = requests.post(**login_post)
            if r'document.location="/pet.jsp"' in login_response.text:
                self.cookies = login_response.cookies
                print('登陆成功')
                return True
            else:
                for _code in _error_code.keys():
                    if f'errCode={_code}"' in login_response.text:
                        if _code == '11' or _code == '999':
                            # 错误码为11,验证码错误,重新获取验证码登录
                            # 错误码为999,封ip,重新获取验证码登录
                            continue
                        else:
                            print(f'出现非验证码错误,错误类型为=>{_error_code[_code]}')
                            return False

    def join_role(self):
        # 进入切换角色开始
        while True:
            changerole_get = {
                'url': f'http://{self.服务器}.pet.imop.com/action.jsp?action=changerole',
                'cookies': self.cookies,
                'headers': {
                        'Content-Type': 'application/x-www-form-urlencoded',
                        'Origin': f'http://{self.服务器}.pet.imop.com',
                        'Referer': f'http://{self.服务器}.pet.imop.com/login.html',
                        'Connection': 'keep-alive',
                        'User-Agent': 'Mozilla/4.0 (compatible; MSIE 7.0; Windows NT 10.0; WOW64; Trident/7.0; .NET4.0C; .NET4.0E)',
                },
                # 'proxies': self.proxies,
            }
            changerole_response = requests.get(**changerole_get)
            if 'selectPet.jsp' in changerole_response.text:
                break
        while True:
            join_get = {
                'url': f'http://{self.服务器}.pet.imop.com/pet.jsp',
                'cookies': self.cookies,
                'headers': {
                        'Content-Type': 'application/x-www-form-urlencoded',
                        'Origin': f'http://{self.服务器}.pet.imop.com',
                        'Referer': f'http://{self.服务器}.pet.imop.com/login.html',
                        'Connection': 'keep-alive',
                        'User-Agent': 'Mozilla/4.0 (compatible; MSIE 7.0; Windows NT 10.0; WOW64; Trident/7.0; .NET4.0C; .NET4.0E)',
                },
            }
            join_response = requests.get(**join_get)
            if 'selectimg' in join_response.text:
                break
        # 进入切换角色完成
        role_boxes = self.patterns['join_role'].findall(join_response.text)
        for role in role_boxes:
            print(f"""角色序号[{role[0]}]角色名[{role[2]}]角色id[{role[1]}]""")
        for role in role_boxes:
            if int(role[0]) == int(self.角色序号):
                self.petId = role[1]
                _pet_url = f'http://{self.服务器}.pet.imop.com/pet.jsp?petid={self.petId}'
                break
        # petId获取成功
        # 进入角色
        while True:
            role_get = {
                'url': _pet_url,
                'cookies': self.cookies,
            }
            role_response = requests.get(**role_get)
            if 'showWorldMap' in role_response.text:
                break
        # 进入角色完成
        print('进入角色完成')
        # 初始化属性
        while True:
            response = requests.get(url=_pet_url, cookies=self.cookies)
            t = response.text
            try:
                self.等级 = self.patterns['petLv'].search(t).group(1)
                self.地图 = self.patterns['nowMap'].search(t).group(1)
                self.房间 = self.patterns['room'].search(t).group(1)
                self.宠物名 = self.patterns['petName'].search(t).group(1)
                self.myUserId = self.patterns['myUserId'].search(t).group(1)
                self.validateParam = self.patterns['validateParam'].search(t).group(1)
                self.hp_left = self.patterns['hp_left'].search(t).group(1)
                self.sp_left = self.patterns['sp_left'].search(t).group(1)
                self.hp_max = self.patterns['hp_max'].search(t).group(1)
                self.sp_max = self.patterns['sp_max'].search(t).group(1)
                self.经验百分比 = int(re.compile(r'<\s*span\s+id\s*=\s*["\']?expBai["\']?\s*>(\d+)\s*</\s*span\s*>', re.IGNORECASE).search(t).group(1))
                self.online_url = f'http://{self.服务器}.pet.imop.com:8080/io/{self.myUserId}&{self.validateParam}'
                print('初始化属性')
                break
            except:
                pass
        return True

    def pull_online(self):
        def _pull_online():
            while True:
                online_get = {
                    'url': self.online_url,
                    'cookies': self.cookies,
                    'stream': True,
                }
                try:
                    with requests.get(**online_get) as response:
                        self.send_orders('look', 0) # 连接上以后先发送look,刷新一下场景
                        buffer = ""
                        print('保持在线')
                        # 启动任务调度器
                        if not self.调度器线程 or not self.调度器线程.is_alive():
                            self.调度器线程 = MyThread(target=self.任务调度器, name=f'scheduler-{self.账号}')
                            self.调度器线程.start()
                            print('任务调度器已启动')
                        for chunk in response.iter_content(chunk_size=1, decode_unicode=True):
                            if chunk:
                                buffer += chunk
                                # 检查是否有完整的<script>标签
                                while '<script>' in buffer and '</script>' in buffer:
                                    start = buffer.find('<script>')
                                    end = buffer.find('</script>') + len('</script>')
                                    if start < end:
                                        script_content = buffer[start:end]
                                        buffer = buffer[end:]
                                        # 提取<script>标签内的内容
                                        inner_content = script_content[8:-9]  # 去掉<script>和</script>
                                        if inner_content.strip():
                                            _function_list = parse_js_code(inner_content)# 解析js代码
                                            for _func in _function_list:
                                                match _func['function_name']:
                                                    case _ if _func['function_name'] in [
                                                        # 不处理函数列表
                                                        'initWorker',   # 初始化函数
                                                        'cls',          # 清空房间
                                                        'cps',          # 清空房间
                                                        'offOpenWin',   # 关闭窗口
                                                        'clsMes',       # Npc对话框清空
                                                        '_roomDesc',     # 房间描述
                                                        'reOnlineNum',  # 在线状态
                                                        'addUser',      # 当前房间增加角色
                                                        'delUser',      # 当前房间删除角色  
                                                        'showRen',      # Npc图片
                                                        'closeRen',     # Npc图片
                                                        'showAlert',    # 白底小提示框
                                                        'win',          # 战斗胜利,但是太快反应会卡指令,所以不作为结束战斗依据
                                                        'closeBossZhanDouInfo',     # 疑似战斗结束,但是不能用,同上
                                                        '_showMiracle', # 无关痛痒,不知道是啥
                                                        'showRenPic',   # NPC图片
                                                        'showRenBigPic',   # NPC图片
                                                    ]:
                                                        continue
                                                    case 'addCM':
                                                        print(f'右上框:{_func['parameters'][0]['value']}')
                                                    case 'addRM':
                                                        print(f'左下框:{_func['parameters'][0]['value']}')
                                                    case 'addMY':
                                                        print(f'右下框:{_func['parameters'][0]['value']}')
                                                    case 'addMessage':
                                                        match _func['parameters'][0]['value']:
                                                            case 'roomReader':
                                                                print(f'左下框:{_func["parameters"][1]["value"]}')
                                                            case _:
                                                                print('待处理addMessage')
                                                    case '_combat':
                                                        self.战斗状态 = True
                                                        print('进入战斗')
                                                    case 'lost':
                                                        self.战斗状态 = False
                                                        print('结束战斗')
                                                    case 'state':
                                                        print('战斗相关,暂不处理')
                                                    case 'att1':
                                                        print('战斗相关,暂不处理')
                                                    case 'addNpcs':
                                                        _s = _func['parameters'][0]['value']
                                                        self.Npc列表 = [i.replace("'", '').replace(" ", '').split(',') for i in self.patterns['npc'].findall(_s)]
                                                        # print('Npc列表变动')
                                                        # print(self.Npc列表)
                                                    case 'setRoom':
                                                        self.房间 = _func['parameters'][0]['value']
                                                        print('房间', self.房间)
                                                    case 'changeMap':
                                                        self.地图 = _func['parameters'][0]['value']
                                                        print('地图', self.地图)
                                                    case 'setMaxHP':
                                                        self.hp_max = _func['parameters'][0]['value']
                                                        print(f'最大hp:{self.hp_max}')
                                                    case 'setMaxSP':
                                                        self.sp_max = _func['parameters'][0]['value']
                                                        print(f'最大sp:{self.sp_max}')
                                                    case 'setLine':
                                                        match _func['parameters'][0]['value']:
                                                            case 'hpLine_left':
                                                                self.hp_left = int(_func['parameters'][-1]['value'])
                                                            case 'mpLine_left':
                                                                self.sp_left = int(_func['parameters'][-1]['value'])
                                                            case 'hpLine_right':
                                                                pass
                                                            case 'mpLine_right':
                                                                pass
                                                    case 'beiDing':
                                                        print(f'被顶号:ip{_func["parameters"][0]['value']},延迟{self.重连间隔}秒后重登')
                                                        time.sleep(self.重连间隔)
                                                    case 'setFightTaskImg':
                                                        print('已检测,待处理')
                                                    case _ if _func['function_name'] in ['showI', 'showIHide']:
                                                        self.背包道具 = ret_packages(_func)
                                                        self.背包道具更新时间 = time.time()
                                                    case '_skillsubs':
                                                        # print(_func['parameters'][0]['value'])
                                                        self.技能列表 = eval(_func['parameters'][0]['value'].replace('false', 'False').replace('true','True'))
                                                        self.技能列表更新时间 = time.time()
                                                    case 'setLv':
                                                        self.等级 = int(_func['parameters'][0]['value'])
                                                        # print(self.等级)
                                                    case 'setExp':
                                                        self.经验百分比 = int(round(int(_func['parameters'][0]['value'])/int(_func['parameters'][2]['value']),4) * 100)
                                                        # print(self.经验百分比)
                                                    case '_petinfo':
                                                        # 宠物信息
                                                        # for i in _func['parameters']:
                                                        #     del i['raw']
                                                        #     print(i)
                                                        continue
                                                    case 'showAllotWin':
                                                        print('分配道具函数处理')
                                                        _s = _func['parameters'][0]['value']
                                                        _number = re.findall(r'p\.allotObj\((\d+)', _s)
                                                        for i in _number:
                                                            # 结尾2是需求, 1是放弃
                                                            self.send_orders(f'foo rank allot {i} 2')
                                                    case 'addNPCC':
                                                        self.Npc对话框内容 = _func['parameters'][0]['value']
                                                        print('Npc对话框:', self.Npc对话框内容)
                                                    case _:
                                                        print('未处理格式')
                                                        print(_func)
                finally:
                    pass
        if self.login():
            if self.join_role():
                self.online_thread = MyThread(target=_pull_online, name=f'online-{self.账号}')
                self.online_thread.start()
    
    def send_orders(self, orders:str|list, delay=None):
        if isinstance(orders, str):
            orders = orders.split('|')
        for order in orders:
            try:
                self.最新命令 = order
                if order == '':
                    continue
                requests.post(
                    url=self.命令地址,
                    cookies=self.cookies,
                    data={
                        'action': 'inputCommand',
                        'inputCommand': ('/' + order).encode('gbk'),
                    }
                )
            except:
                pass
            if delay is None:
                time.sleep(self.延迟)
            else:
                time.sleep(delay)
    
    def gto(self, room):
        寻路起点 = self.地图 + '|' + self.房间
        while True:
            print(f'从{寻路起点}到{room}')
            self.send_orders(f'relive|look|{ret_way(寻路起点, room)}')
            if self.地图 + '|' + self.房间 == room:
                break
            寻路起点 = self.地图 + '|' + self.房间

    def 日常_拖把(self):
        _markpoint = self.地图 + '|' + self.房间        # 标记当前位置
        self.gto('map.mopcity|驿站')
        self.send_orders(daily_tb_orders)
        self.gto(_markpoint)        # 回到标记点

    def 日常_魔化(self):
        _markpoint = self.地图 + '|' + self.房间        # 标记当前位置
        self.gto('map.jixiedao|机械岛接待室')
        self.send_orders(daily_mh_orders)
        self.gto(_markpoint)

    def 活动_如意(self):
        _markpoint = self.地图 + '|' + self.房间        # 标记当前位置
        self.gto('map.mopcity|添香楼')
        self.send_orders('getrybp 2')
        self.gto(_markpoint)

    def 闲时_禅壹(self):
        if f'{self.地图}|{self.房间}' != 'chan|禅壹':
            self.gto('chan|禅壹')
            time.sleep(60)

    def 闲时_禅伍(self):
        if f'{self.地图}|{self.房间}' != 'chan|禅伍':
            self.gto('chan|禅伍')
            time.sleep(60)
    
    def 闲时_道壹(self):
        if f'{self.地图}|{self.房间}' != 'dao|道壹':
            self.gto('dao|道壹')
            time.sleep(60)
    
    def 活动_通天(self):
        while True:
            # 当前房间不包含通天塔字样,不在指定位置
            if '通天塔' not in self.房间:
                self.gto('map.baimagang|通天塔')
            # 当前房间在通天塔门口
            if self.房间 == '通天塔':
                self.send_orders('comein yes', 0.25)
                continue
            if '通天塔' in self.房间 and '层' in self.房间:
                # TODO 打一次怪                
                self.send_orders('gotonext')
                # TODO 

    def 活动_逆无双(self):
        pass
    
    def 活动_罗汉(self):
        pass

    def 特殊_大懒怪(self):
        while len(self.Npc列表) == 0:
            time.sleep(0.1)
        for i in self.Npc列表.copy():
            if '树蛙' in i[1]:
                self.send_orders(f'bar34 {i[0]} {i[2]}')
                break
        keyboard.wait(';')

    def 任务调度器(self):
        """
        任务调度器 - 管理日常、闲时、活动三类任务的执行优先级
        优先级: 活动 > 日常 > 闲时
        """
        import datetime

        def 检查日常任务():
            """检查是否有日常任务需要执行"""
            今天 = datetime.date.today().isoformat()
            需要执行的日常任务 = []

            # 检查普通日常任务（每天执行一次）
            普通日常任务映射 = {
                '拖把': (self.日常拖把开关, self.日常_拖把),
                '魔化': (self.日常魔化开关, self.日常_魔化),
            }

            for 任务名, (开关, 函数) in 普通日常任务映射.items():
                if 开关 and self.上次日常执行日期.get(任务名) != 今天:
                    需要执行的日常任务.append((任务名, 函数))



            return 需要执行的日常任务

        def 检查活动任务():
            """检查是否有活动任务需要执行"""
            现在 = datetime.datetime.now()
            当前星期 = 现在.weekday()  # 0=周一, 6=周日
            当前时间 = 现在.time()

            # 固定的活动任务时间安排
            活动时间表 = {
                '通天': {
                    '星期': [1, 5],  # 周二、周六 (0=周一)
                    '开始时间': datetime.time(20, 0),
                    '结束时间': datetime.time(20, 59),
                    '函数': self.活动_通天
                },
                '逆无双': {
                    '星期': [0, 2, 4],  # 周一、三、五
                    '开始时间': datetime.time(20, 0),
                    '结束时间': datetime.time(20, 59),
                    '函数': self.活动_逆无双
                },
                '罗汉': {
                    '星期': [1, 3],  # 周二、四
                    '开始时间': datetime.time(19, 0),
                    '结束时间': datetime.time(19, 59),
                    '函数': self.活动_罗汉
                },
                '如意': {
                    '时间段': [
                        (datetime.time(16, 13), datetime.time(16, 29)),   # 12:00-12:29
                        (datetime.time(17, 30), datetime.time(17, 59)),  # 17:30-17:59
                        (datetime.time(21, 0), datetime.time(21, 29)),   # 21:00-21:29
                    ],
                    '函数': self.活动_如意
                }
            }

            # 检查每个活动任务
            for 活动名, 配置 in 活动时间表.items():
                任务函数 = 配置['函数']

                if 活动名 == '如意':
                    # 如意任务特殊处理：检查是否在指定时间段内
                    if self.日常如意开关:
                        for 开始时间, 结束时间 in 配置['时间段']:
                            if 开始时间 <= 当前时间 <= 结束时间:
                                # 检查这个时间段是否已经执行过
                                今天 = datetime.date.today().isoformat()
                                时间段标识 = f"如意_{开始时间.hour}_{开始时间.minute}"
                                if self.上次日常执行日期.get(时间段标识) != 今天:
                                    return (时间段标识, 任务函数)
                                break
                else:
                    # 其他活动任务按星期和时间检查
                    允许星期 = 配置['星期']
                    开始时间 = 配置['开始时间']
                    结束时间 = 配置['结束时间']

                    if (当前星期 in 允许星期 and 开始时间 <= 当前时间 <= 结束时间):
                        return (活动名, 任务函数)

            return None

        def 获取闲时任务():
            """获取闲时任务"""
            if not self.闲时任务:
                return None

            # 根据配置的闲时任务名称返回对应的函数
            闲时任务映射 = {
                '闲时_禅壹': ('禅壹', self.闲时_禅壹),
                '闲时_禅伍': ('禅伍', self.闲时_禅伍),
                '闲时_道壹': ('道壹', self.闲时_道壹),
            }

            return 闲时任务映射.get(self.闲时任务, None)

        def 执行任务(任务类型, 任务名, 任务函数):
            """执行指定任务"""
            try:
                print(f'开始执行{任务类型}任务: {任务名}')
                self.当前任务类型 = 任务类型
                self.当前任务名称 = 任务名

                # 创建任务线程
                self.任务线程 = MyThread(target=任务函数, name=f'{任务类型}-{任务名}')
                self.任务线程.start()

                # 如果是日常任务或如意活动任务，记录执行日期
                if 任务类型 == 'daily' or (任务类型 == 'activity' and '如意' in 任务名):
                    今天 = datetime.date.today().isoformat()
                    self.上次日常执行日期[任务名] = 今天

                return True
            except Exception as e:
                print(f'执行任务失败: {e}')
                return False

        def 中断当前任务():
            """中断当前正在执行的任务"""
            if self.任务线程 and self.任务线程.is_alive():
                print(f'中断当前任务: {self.当前任务类型}-{self.当前任务名称}')
                self.任务线程.destroy()
                self.任务线程 = None
                self.当前任务类型 = None
                self.当前任务名称 = None
                return True
            return False

        # 主调度循环
        while True:
            try:
                # 1. 检查活动任务（最高优先级）
                活动任务 = 检查活动任务()
                if 活动任务:
                    任务名, 任务函数 = 活动任务
                    if self.当前任务类型 != 'activity' or self.当前任务名称 != 任务名:
                        中断当前任务()
                        执行任务('activity', 任务名, 任务函数)

                # 2. 检查日常任务（中等优先级）
                elif not (self.当前任务类型 == 'activity'):
                    日常任务列表 = 检查日常任务()
                    if 日常任务列表:
                        任务名, 任务函数 = 日常任务列表[0]  # 取第一个待执行的日常任务
                        # 同等级任务不互相中断，只有当前没有日常任务在执行时才启动新的日常任务
                        if self.当前任务类型 != 'daily':
                            中断当前任务()
                            执行任务('daily', 任务名, 任务函数)
                        elif self.当前任务名称 != 任务名 and (not self.任务线程 or not self.任务线程.is_alive()):
                            # 当前日常任务已完成，可以执行下一个日常任务
                            执行任务('daily', 任务名, 任务函数)

                    # 3. 执行闲时任务（最低优先级）
                    else:
                        # 当前没有日常任务需要执行，且没有任务在运行时，执行闲时任务
                        if not self.任务线程 or not self.任务线程.is_alive():
                            闲时任务 = 获取闲时任务()
                            if 闲时任务:
                                任务名, 任务函数 = 闲时任务
                                执行任务('闲时任务', 任务名, 任务函数)

                # 检查当前任务是否完成
                if self.任务线程 and not self.任务线程.is_alive():
                    print(f'任务完成: {self.当前任务类型}-{self.当前任务名称}')
                    self.任务线程 = None
                    self.当前任务类型 = None
                    self.当前任务名称 = None

                # 调度间隔
                time.sleep(1)

            except Exception as e:
                print(f'任务调度器异常: {e}')
                time.sleep(5)

    def 主函数(self):
        """测试任务调度器功能"""
        print(f'=== {self.账号} 任务调度器测试开始 ===')

        # 显示当前配置
        print('当前任务开关状态:')
        print(f'  日常拖把: {self.日常拖把开关}')
        print(f'  日常魔化: {self.日常魔化开关}')
        print(f'  活动任务配置: {self.活动任务配置}')

        # 显示当前状态
        print(f'\n当前状态:')
        print(f'  地图: {self.地图}')
        print(f'  房间: {self.房间}')
        print(f'  等级: {self.等级}')
        print(f'  宠物名: {self.宠物名}')

        # 监控任务调度器状态
        try:
            while True:
                if self.调度器线程:
                    print(f'\n调度器状态: {self.调度器线程.get_state().value}')
                    print(f'当前任务: {self.当前任务类型}-{self.当前任务名称}')
                    if self.任务线程:
                        print(f'任务线程状态: {self.任务线程.get_state().value}')
                else:
                    print('\n调度器未启动')

                time.sleep(10)  # 每10秒显示一次状态

        except KeyboardInterrupt:
            print('\n测试结束')
        except Exception as e:
            print(f'\n测试异常: {e}')
        
    def __del__(self):
        # 销毁所有线程
        if self.online_thread and self.online_thread._state != ThreadState.DESTROYED:
            self.online_thread.destroy()
        if self.调度器线程 and self.调度器线程._state != ThreadState.DESTROYED:
            self.调度器线程.destroy()
        if self.任务线程 and self.任务线程._state != ThreadState.DESTROYED:
            self.任务线程.destroy()
        print('所有线程已销毁')
        

def function_test():
    config = {
        "服务器": "x12",
        "账号": "memorysiliao",
        "密码": "000000",
        "角色序号": "0",
        "代理配置": {
            "代理账号":"FQTUENGM",
            "代理密码":"35B7E37DAF97"
        },
        # 测试配置
        "日常拖把开关": True,
        "日常魔化开关": True,
        # 启用如意日常任务测试
        "日常如意开关": True,
        # 闲时任务配置
        "闲时任务": "闲时_禅壹"
    }

    print('开始测试任务调度器...')
    myj = MYJ(config)

    # 等待登录和初始化完成
    time.sleep(10)

    # 运行主函数进行测试
    myj.主函数()

    # 清理
    del myj
    
if __name__ == '__main__':
    function_test()
    