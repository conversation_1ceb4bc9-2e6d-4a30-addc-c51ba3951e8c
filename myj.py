# 第一部分 库
from urllib.parse import quote  # 登录时,把中文账号转码使用
import requests                 # 请求通用,网络
import time                     # 全局通用,时间
import re                       # 全局通用,正则
import keyboard                 # 全局通用,键盘
import json                     # 全局通用,json
import datetime                 # 全局通用,日期时间
# 第二部分 自写代码
from mythread import MyThread, ThreadState   # 系统级线程,全局使用
from default import default_speed, default_reconnect, daily_tb_orders, daily_mh_orders   # 内置默认频率,单位秒
from proxy import get_proxy         # 获取代理
from yzm import get_yzm             # 获取验证码
from js_parser import parse_js_code, ret_packages # 解析js代码
from map import ret_way


class MYJ:
    def __init__(self, config):
        # 内置默认配置
        self.延迟 = default_speed  # 内置默认频率,单位秒
        self.服务器 = config['服务器']
        self.账号 = config['账号']
        self.密码 = config['密码']
        self.角色序号 = config['角色序号']
        self.代理配置 = config.get('代理配置', None)
        self.proxies = None        
        self.cookies = None
        self.petId = None
        self.myUserId = None
        self.validateParam = None
        self.patterns = {
            'npc': re.compile(r"p\._getNpc\(([^)]*)\)"),
            'modify': re.compile(r'<[A-Za-z0-9 ="\':;/_\-.]*>'),
            'petLv': re.compile(r"petLv=(\d+);"),   # 宠物等级
            'nowMap': re.compile(r'nowMap\s*=\s*"([^"]+)"'),    # 当前所在地图
            'room': re.compile(r'room\s*=\s*"([^"]+)"'),    # 当前所在房间
            'hp_left': re.compile(r"<span id='hpLine_left_no'[^>]*>(\d+)</span>"),  # 剩余hp
            'hp_max': re.compile(r"<span id='hpLine_left_max_no'[^>]*>(\d+)</span>"),   # 最大hp
            'sp_left': re.compile(r"<span id='mpLine_left_no'[^>]*>(\d+)</span>"),  # 剩余sp
            'sp_max': re.compile(r"<span id='mpLine_left_max_no'>(\d+)</span>"),    # 最大sp
            'exp': re.compile(r'<\s*span\s+id\s*=\s*["\']?expBai["\']?\s*>(\d+)\s*</\s*span\s*>', re.IGNORECASE),   # 百分比经验
            'join_role': re.compile(r"selectimg\(\s*(\d+)\s*,\s*'click'\s*,\s*(\d+)[^)]*\)\s*.*?"
                                    r'<div\s+align="center"\s+style="position:relative;top:-10px">(.*?)</div>', re.DOTALL),  # 获取角色代码及序号部分
            # 'skills': re.compile(r"p\.cutArray\[(?:[0-5])\]='(perform.*?)';"),  #
            'petName': re.compile(r'petName\s?=\s?"(.*)";'),    # 宠物名
            'myUserId': re.compile(r'myUserId\s?=\s?"(.*)";'),  # id
            'validateParam': re.compile(r'validateParam\s?=\s?"(.*)";'),    # 链接验证参数
            'combat_start': re.compile(r"p\._combat\s*\(", re.IGNORECASE),      # 进入战斗
            'combat_end': re.compile(r"p\.lost\(\s*p\.petWin\.fighter_2\s*\)", re.IGNORECASE),  # 退出战斗
        }
        self.online_url = None
        self.online_thread = None
        self.战斗状态 = False
        self.Npc列表 = []
        self.房间 = None
        self.地图 = None
        self.重连间隔 = default_reconnect
        self.背包道具 = []
        self.背包道具更新时间 = None
        self.职业技能 = None
        self.技能列表 = []
        self.技能列表更新时间 = None
        self.等级 = None
        self.宠物名 = None
        self.hp_left = None
        self.sp_left = None
        self.hp_max = None
        self.sp_max = None
        self.经验百分比 = None
        self.最新命令 = None
        self.Npc对话框内容 = None
        self.命令地址 = f'http://{self.服务器}.pet.imop.com/action.jsp?'
        self.日常拖把开关 = config.get('日常拖把开关', True)
        self.日常礼包开关 = config.get('日常礼包开关', True)
        self.日常竞技场开关 = config.get('日常竞技场开关', False)
        self.日常魔化开关 = config.get('日常魔化开关', False)
        self.日常如意开关 = config.get('日常如意开关', False)
        self.日常乐园开关 = config.get('日常乐园开关', False)
        self.日常银月开关 = config.get('日常银月开关', False)
        self.日常还猪开关 = config.get('日常还猪开关', False)
        self.日常沼泽开关 = config.get('日常沼泽开关', False)
        self.日常小镇开关 = config.get('日常小镇开关', False)
        self.闲时任务 = config.get('闲时任务', None)
        self.今日已执行活动 = {}  # 记录今日已执行的活动任务

        self.pull_online()  # 初始化完了以后直接保持在线
    
    def 确认服务器登录状态(self):
        while True:
            print('获取代理配置')
            self.proxies = get_proxy(self.代理配置.get('代理账号'), self.代理配置.get('代理密码')) if self.代理配置 else None
            if self.proxies is not None:
                print('获取代理配置成功')
                _temp_frequency = 30
                _temp_sleep = 0.5
            else :
                print('获取代理配置失败')
                _temp_frequency = 30
                _temp_sleep = 1.51
            for i in range(_temp_frequency):
                res = requests.get(f'http://{self.服务器}.pet.imop.com')
                if res.status_code == 503:
                    print(f'服务器{self.服务器}维护未完成')
                    time.slee(_temp_sleep)
                else:
                    # print(res.status_code)
                    print(f'服务器{self.服务器}不在维护,可登录')
                    return True

    def login(self):
        # 登录错误码
        _error_code = {
            '0': "登录失败,请重新登录",
            '1': "签名验证失败",
            '2': "时间戳过期",
            '3': "参数为空或格式不正确",
            '4': "用户名密码验证未通过",
            '5': "用户已被锁定",
            '6': "密保未通过",
            '7': "cookie验证未通过",
            '8': "token验证未通过",
            '9': "大区验证未通过",
            '11': "验证码错误",
            '12': "验证码为空",
            '999': "系统异常，登录失败"
        }
        # 登录信息
        self.确认服务器登录状态()
        while True:
            # 登录发送信息
            self.proxies = get_proxy(self.代理配置.get('代理账号'), self.代理配置.get('代理密码')) if self.代理配置 else None
            login_post = {
                'url': f'http://{self.服务器}.pet.imop.com/LoginAction.jsp',
                'cookies': {'mopet_logon': '123'},
                'headers': {
                    'Content-Type': 'application/x-www-form-urlencoded',
                    'Origin': f'http://{self.服务器}.pet.imop.com',
                    'Referer': f'http://{self.服务器}.pet.imop.com/login.html',
                    'Connection': 'keep-alive',
                    'User-Agent': 'Mozilla/4.0 (compatible; MSIE 7.0; Windows NT 10.0; WOW64; Trident/7.0; .NET4.0C; .NET4.0E)',
                },
                'data': {
                    'user_name': quote(self.账号, encoding="gbk"),
                    'password': self.密码,
                    'checkcode': get_yzm(proxies=self.proxies)
                },
                'proxies': self.proxies,
            }
            # 登录返回信息
            login_response = requests.post(**login_post)
            if r'document.location="/pet.jsp"' in login_response.text:
                self.cookies = login_response.cookies
                print('登陆成功')
                return True
            else:
                for _code in _error_code.keys():
                    if f'errCode={_code}"' in login_response.text:
                        if _code == '11' or _code == '999':
                            # 错误码为11,验证码错误,重新获取验证码登录
                            # 错误码为999,封ip,重新获取验证码登录
                            continue
                        else:
                            print(f'出现非验证码错误,错误类型为=>{_error_code[_code]}')
                            return False

    def join_role(self):
        # 进入切换角色开始
        while True:
            changerole_get = {
                'url': f'http://{self.服务器}.pet.imop.com/action.jsp?action=changerole',
                'cookies': self.cookies,
                'headers': {
                        'Content-Type': 'application/x-www-form-urlencoded',
                        'Origin': f'http://{self.服务器}.pet.imop.com',
                        'Referer': f'http://{self.服务器}.pet.imop.com/login.html',
                        'Connection': 'keep-alive',
                        'User-Agent': 'Mozilla/4.0 (compatible; MSIE 7.0; Windows NT 10.0; WOW64; Trident/7.0; .NET4.0C; .NET4.0E)',
                },
                # 'proxies': self.proxies,
            }
            changerole_response = requests.get(**changerole_get)
            if 'selectPet.jsp' in changerole_response.text:
                break
        while True:
            join_get = {
                'url': f'http://{self.服务器}.pet.imop.com/pet.jsp',
                'cookies': self.cookies,
                'headers': {
                        'Content-Type': 'application/x-www-form-urlencoded',
                        'Origin': f'http://{self.服务器}.pet.imop.com',
                        'Referer': f'http://{self.服务器}.pet.imop.com/login.html',
                        'Connection': 'keep-alive',
                        'User-Agent': 'Mozilla/4.0 (compatible; MSIE 7.0; Windows NT 10.0; WOW64; Trident/7.0; .NET4.0C; .NET4.0E)',
                },
            }
            join_response = requests.get(**join_get)
            if 'selectimg' in join_response.text:
                break
        # 进入切换角色完成
        role_boxes = self.patterns['join_role'].findall(join_response.text)
        for role in role_boxes:
            print(f"""角色序号[{role[0]}]角色名[{role[2]}]角色id[{role[1]}]""")
        for role in role_boxes:
            if int(role[0]) == int(self.角色序号):
                self.petId = role[1]
                _pet_url = f'http://{self.服务器}.pet.imop.com/pet.jsp?petid={self.petId}'
                break
        # petId获取成功
        # 进入角色
        while True:
            role_get = {
                'url': _pet_url,
                'cookies': self.cookies,
            }
            role_response = requests.get(**role_get)
            if 'showWorldMap' in role_response.text:
                break
        # 进入角色完成
        print('进入角色完成')
        # 初始化属性
        while True:
            response = requests.get(url=_pet_url, cookies=self.cookies)
            t = response.text
            try:
                self.等级 = self.patterns['petLv'].search(t).group(1)
                self.地图 = self.patterns['nowMap'].search(t).group(1)
                self.房间 = self.patterns['room'].search(t).group(1)
                self.宠物名 = self.patterns['petName'].search(t).group(1)
                self.myUserId = self.patterns['myUserId'].search(t).group(1)
                self.validateParam = self.patterns['validateParam'].search(t).group(1)
                self.hp_left = self.patterns['hp_left'].search(t).group(1)
                self.sp_left = self.patterns['sp_left'].search(t).group(1)
                self.hp_max = self.patterns['hp_max'].search(t).group(1)
                self.sp_max = self.patterns['sp_max'].search(t).group(1)
                self.经验百分比 = int(re.compile(r'<\s*span\s+id\s*=\s*["\']?expBai["\']?\s*>(\d+)\s*</\s*span\s*>', re.IGNORECASE).search(t).group(1))
                self.online_url = f'http://{self.服务器}.pet.imop.com:8080/io/{self.myUserId}&{self.validateParam}'
                print('初始化属性')
                break
            except:
                pass
        return True

    def pull_online(self):
        def _pull_online():
            while True:
                online_get = {
                    'url': self.online_url,
                    'cookies': self.cookies,
                    'stream': True,
                }
                try:
                    with requests.get(**online_get) as response:
                        self.send_orders('look', 0) # 连接上以后先发送look,刷新一下场景
                        buffer = ""
                        print('保持在线')
                        for chunk in response.iter_content(chunk_size=1, decode_unicode=True):
                            if chunk:
                                buffer += chunk
                                # 检查是否有完整的<script>标签
                                while '<script>' in buffer and '</script>' in buffer:
                                    start = buffer.find('<script>')
                                    end = buffer.find('</script>') + len('</script>')
                                    if start < end:
                                        script_content = buffer[start:end]
                                        buffer = buffer[end:]
                                        # 提取<script>标签内的内容
                                        inner_content = script_content[8:-9]  # 去掉<script>和</script>
                                        if inner_content.strip():
                                            _function_list = parse_js_code(inner_content)# 解析js代码
                                            for _func in _function_list:
                                                match _func['function_name']:
                                                    case _ if _func['function_name'] in [
                                                        # 不处理函数列表
                                                        'initWorker',   # 初始化函数
                                                        'cls',          # 清空房间
                                                        'cps',          # 清空房间
                                                        'offOpenWin',   # 关闭窗口
                                                        'clsMes',       # Npc对话框清空
                                                        '_roomDesc',     # 房间描述
                                                        'reOnlineNum',  # 在线状态
                                                        'addUser',      # 当前房间增加角色
                                                        'delUser',      # 当前房间删除角色  
                                                        'showRen',      # Npc图片
                                                        'closeRen',     # Npc图片
                                                        'showAlert',    # 白底小提示框
                                                        'win',          # 战斗胜利,但是太快反应会卡指令,所以不作为结束战斗依据
                                                        'closeBossZhanDouInfo',     # 疑似战斗结束,但是不能用,同上
                                                        '_showMiracle', # 无关痛痒,不知道是啥
                                                        'showRenPic',   # NPC图片
                                                        'showRenBigPic',   # NPC图片
                                                        _
                                                    ]:
                                                        continue
                                                    case 'addCM':
                                                        print(f'右上框:{_func['parameters'][0]['value']}')
                                                    case 'addRM':
                                                        print(f'左下框:{_func['parameters'][0]['value']}')
                                                    case 'addMY':
                                                        print(f'右下框:{_func['parameters'][0]['value']}')
                                                    case 'addMessage':
                                                        match _func['parameters'][0]['value']:
                                                            case 'roomReader':
                                                                print(f'左下框:{_func["parameters"][1]["value"]}')
                                                            case _:
                                                                print('待处理addMessage')
                                                    case '_combat':
                                                        self.战斗状态 = True
                                                        print('进入战斗')
                                                    case 'lost':
                                                        self.战斗状态 = False
                                                        print('结束战斗')
                                                    case 'state':
                                                        print('战斗相关,暂不处理')
                                                    case 'att1':
                                                        print('战斗相关,暂不处理')
                                                    case 'addNpcs':
                                                        _s = _func['parameters'][0]['value']
                                                        self.Npc列表 = [i.replace("'", '').replace(" ", '').split(',') for i in self.patterns['npc'].findall(_s)]
                                                        # print('Npc列表变动')
                                                        # print(self.Npc列表)
                                                    case 'setRoom':
                                                        self.房间 = _func['parameters'][0]['value']
                                                        print('房间', self.房间)
                                                    case 'changeMap':
                                                        self.地图 = _func['parameters'][0]['value']
                                                        print('地图', self.地图)
                                                    case 'setMaxHP':
                                                        self.hp_max = _func['parameters'][0]['value']
                                                        print(f'最大hp:{self.hp_max}')
                                                    case 'setMaxSP':
                                                        self.sp_max = _func['parameters'][0]['value']
                                                        print(f'最大sp:{self.sp_max}')
                                                    case 'setLine':
                                                        match _func['parameters'][0]['value']:
                                                            case 'hpLine_left':
                                                                self.hp_left = int(_func['parameters'][-1]['value'])
                                                            case 'mpLine_left':
                                                                self.sp_left = int(_func['parameters'][-1]['value'])
                                                            case 'hpLine_right':
                                                                pass
                                                            case 'mpLine_right':
                                                                pass
                                                    case 'beiDing':
                                                        print(f'被顶号:ip{_func["parameters"][0]['value']},延迟{self.重连间隔}秒后重登')
                                                        time.sleep(self.重连间隔)
                                                    case 'setFightTaskImg':
                                                        print('已检测,待处理')
                                                    case _ if _func['function_name'] in ['showI', 'showIHide']:
                                                        self.背包道具 = ret_packages(_func)
                                                        self.背包道具更新时间 = time.time()
                                                    case '_skillsubs':
                                                        # print(_func['parameters'][0]['value'])
                                                        self.技能列表 = eval(_func['parameters'][0]['value'].replace('false', 'False').replace('true','True'))
                                                        self.技能列表更新时间 = time.time()
                                                    case 'setLv':
                                                        self.等级 = int(_func['parameters'][0]['value'])
                                                        # print(self.等级)
                                                    case 'setExp':
                                                        self.经验百分比 = int(round(int(_func['parameters'][0]['value'])/int(_func['parameters'][2]['value']),4) * 100)
                                                        # print(self.经验百分比)
                                                    case '_petinfo':
                                                        # 宠物信息
                                                        # for i in _func['parameters']:
                                                        #     del i['raw']
                                                        #     print(i)
                                                        continue
                                                    case 'showAllotWin':
                                                        print('分配道具函数处理')
                                                        _s = _func['parameters'][0]['value']
                                                        _number = re.findall(r'p\.allotObj\((\d+)', _s)
                                                        for i in _number:
                                                            # 结尾2是需求, 1是放弃
                                                            self.send_orders(f'foo rank allot {i} 2')
                                                    case 'addNPCC':
                                                        self.Npc对话框内容 = _func['parameters'][0]['value']
                                                        print('Npc对话框:', self.Npc对话框内容)
                                                    case 'showTask':
                                                        _t = _func['parameters'][0]['value']
                                                        print('任务列表', type(_t))
                                                        print(_t)
                                                    case _:
                                                        print('未处理格式')
                                                        print(_func)
                finally:
                    pass
        if self.login():
            if self.join_role():
                self.online_thread = MyThread(target=_pull_online, name=f'online-{self.账号}')
                self.online_thread.start()
    
    def send_orders(self, orders:str, delay=None):
        orders = orders.split('|')
        for order in orders:
            if order == '':
                continue
            try:
                self.最新命令 = order
                requests.post(
                    url=self.命令地址,
                    cookies=self.cookies,
                    data={
                        'action': 'inputCommand',
                        'inputCommand': ('/' + order).encode('gbk'),
                    }
                )
            except:
                pass
            if delay is None:
                time.sleep(self.延迟)
            else:
                time.sleep(delay)
    
    def gto(self, room):
        while True:
            寻路起点 = self.地图 + '|' + self.房间
            print(f'从{寻路起点}到{room}')
            _way = ret_way(寻路起点, room)
            print(_way)
            self.send_orders(f'relive|look|{_way}')
            if 寻路起点 == room:
                break

    def 日常_拖把(self):
        _markpoint = self.地图 + '|' + self.房间        # 标记当前位置
        self.gto('map.mopcity|驿站')
        self.send_orders(daily_tb_orders)
        self.gto(_markpoint)        # 回到标记点

    def 日常_魔化(self):
        _markpoint = self.地图 + '|' + self.房间        # 标记当前位置
        self.gto('map.jixiedao|机械岛接待室')
        self.send_orders(daily_mh_orders)
        self.gto(_markpoint)

    def 活动_如意(self):
        _markpoint = self.地图 + '|' + self.房间        # 标记当前位置
        self.gto('map.mopcity|添香楼')
        self.send_orders('getrybp 2')
        self.gto(_markpoint)

    def 闲时_禅壹(self):
        if f'{self.地图}|{self.房间}' != 'chan|禅壹':
            self.gto('chan|禅壹')

    def 闲时_禅伍(self):
        if f'{self.地图}|{self.房间}' != 'chan|禅伍':
            self.gto('chan|禅伍')
    
    def 闲时_道壹(self):
        if f'{self.地图}|{self.房间}' != 'dao|道壹':
            self.gto('dao|道壹')
    
    def 活动_通天(self):
        while True:
            # 当前房间不包含通天塔字样,不在指定位置
            if '通天塔' not in self.房间:
                self.gto('map.baimagang|通天塔')
            # 当前房间在通天塔门口
            if self.房间 == '通天塔':
                self.send_orders('comein yes', 0.25)
                continue
            if '通天塔' in self.房间 and '层' in self.房间:
                # TODO 打一次怪                
                self.send_orders('gotonext')
                # TODO 

    def 活动_逆无双(self):
        pass
    
    def 活动_罗汉(self):
        pass

    def 特殊_大懒怪(self):
        while len(self.Npc列表) == 0:
            time.sleep(0.1)
        for i in self.Npc列表.copy():
            if '树蛙' in i[1]:
                self.send_orders(f'bar34 {i[0]} {i[2]}')
                break
        keyboard.wait(';')

    def 主函数(self):
        """
        主任务调度函数 - 基于时间驱动的任务管理
        """

        def 执行日常任务():
            """执行所有启用的日常任务"""
            print('=== 开始执行日常任务 ===')

            # 日常任务列表
            日常任务列表 = []
            if self.日常拖把开关:
                日常任务列表.append(('拖把', self.日常_拖把))
            if self.日常魔化开关:
                日常任务列表.append(('魔化', self.日常_魔化))

            # 依次执行日常任务
            for 任务名, 任务函数 in 日常任务列表:
                print(f'执行日常任务: {任务名}')
                try:
                    任务函数()
                    print(f'日常任务 {任务名} 完成')
                except Exception as e:
                    print(f'日常任务 {任务名} 执行异常: {e}')

            print('=== 日常任务全部完成 ===')

        def 检查活动时间():
            """检查当前是否为活动时间，返回活动名称和函数，或None"""
            现在 = datetime.datetime.now()
            当前星期 = 现在.weekday()  # 0=周一, 6=周日
            当前时间 = 现在.time()
            今天 = 现在.date().isoformat()

            # 活动时间表
            活动时间表 = {
                '通天': {
                    '星期': [1, 5],  # 周二、周六
                    '开始时间': datetime.time(20, 0),
                    '结束时间': datetime.time(20, 59),
                    '函数': self.活动_通天
                },
                '逆无双': {
                    '星期': [0, 2, 4],  # 周一、三、五
                    '开始时间': datetime.time(20, 0),
                    '结束时间': datetime.time(20, 59),
                    '函数': self.活动_逆无双
                },
                '罗汉': {
                    '星期': [1, 3],  # 周二、四
                    '开始时间': datetime.time(19, 0),
                    '结束时间': datetime.time(19, 59),
                    '函数': self.活动_罗汉
                }
            }

            # 检查如意活动（特殊处理）
            if self.日常如意开关:
                如意时间段 = [
                    (datetime.time(12, 0), datetime.time(12, 29), '如意_12'),
                    (datetime.time(17, 30), datetime.time(17, 59), '如意_17'),
                    (datetime.time(21, 0), datetime.time(21, 29), '如意_21'),
                ]
                for 开始时间, 结束时间, 时间段标识 in 如意时间段:
                    if 开始时间 <= 当前时间 <= 结束时间:
                        # 检查今日该时间段是否已执行
                        执行标识 = f"{今天}_{时间段标识}"
                        if 执行标识 not in self.今日已执行活动:
                            return (时间段标识, self.活动_如意)

            # 检查其他活动
            for 活动名, 配置 in 活动时间表.items():
                允许星期 = 配置['星期']
                开始时间 = 配置['开始时间']
                结束时间 = 配置['结束时间']
                任务函数 = 配置['函数']

                if (当前星期 in 允许星期 and 开始时间 <= 当前时间 <= 结束时间):
                    # 检查今日该活动是否已执行
                    执行标识 = f"{今天}_{活动名}"
                    if 执行标识 not in self.今日已执行活动:
                        return (活动名, 任务函数)

            return None

        def 执行闲时任务():
            """执行闲时任务"""
            if not self.闲时任务:
                return None

            闲时任务映射 = {
                '闲时_禅壹': self.闲时_禅壹,
                '闲时_禅伍': self.闲时_禅伍,
                '闲时_道壹': self.闲时_道壹,
            }

            任务函数 = 闲时任务映射.get(self.闲时任务)
            if 任务函数:
                print(f'开始执行闲时任务: {self.闲时任务}')
                闲时线程 = MyThread(target=任务函数, name=f'idle-{self.闲时任务}')
                闲时线程.start()
                return 闲时线程
            return None

        def 是否日期切换时间():
            """检查是否为日期切换时间 (23:50-0:10)"""
            现在 = datetime.datetime.now()
            当前时间 = 现在.time()

            # 23:50-23:59 或 0:00-0:10
            if (datetime.time(23, 50) <= 当前时间 <= datetime.time(23, 59) or
                datetime.time(0, 0) <= 当前时间 <= datetime.time(0, 10)):
                return True
            return False

        # 主循环 - 每天的任务周期
        while True:
            try:
                print(f'\n=== 新的一天开始 {datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")} ===')

                # 清空今日已执行活动记录
                self.今日已执行活动.clear()

                # 1. 执行日常任务
                执行日常任务()

                # 2. 进入时间段循环
                当前闲时线程 = None

                while True:
                    # 检查是否为日期切换时间
                    if 是否日期切换时间():
                        print('进入日期切换时间，暂停所有任务...')
                        if 当前闲时线程 and 当前闲时线程.is_alive():
                            当前闲时线程.destroy()
                            当前闲时线程 = None

                        # 等待到0:11再继续
                        while 是否日期切换时间():
                            time.sleep(30)  # 每30秒检查一次

                        print('日期切换完成，进入新的一天')
                        break  # 跳出时间段循环，进入新一天

                    # 检查活动时间
                    活动任务 = 检查活动时间()

                    if 活动任务:
                        活动名, 活动函数 = 活动任务

                        # 停止闲时任务
                        if 当前闲时线程 and 当前闲时线程.is_alive():
                            print(f'停止闲时任务，准备执行活动: {活动名}')
                            当前闲时线程.destroy()
                            当前闲时线程 = None

                        # 执行活动任务
                        print(f'开始执行活动任务: {活动名}')
                        try:
                            活动函数()
                            print(f'活动任务 {活动名} 完成')

                            # 标记该活动今日已执行
                            今天 = datetime.datetime.now().date().isoformat()
                            执行标识 = f"{今天}_{活动名}"
                            self.今日已执行活动[执行标识] = True

                        except Exception as e:
                            print(f'活动任务 {活动名} 执行异常: {e}')

                    else:
                        # 非活动时间，执行闲时任务
                        if not 当前闲时线程 or not 当前闲时线程.is_alive():
                            当前闲时线程 = 执行闲时任务()

                    # 每分钟检查一次
                    time.sleep(60)

            except Exception as e:
                print(f'主函数异常: {e}')
                time.sleep(60)
        
    def __del__(self):
        if self.online_thread and self.online_thread._state != ThreadState.DESTROYED:
            self.online_thread.destroy()
        print('线程已销毁')


def function_test():
    config = {
        "服务器": "x12",
        "账号": "memorysiliao",
        "密码": "000000",
        "角色序号": "0",
        "代理配置": {
            "代理账号":"FQTUENGM",
            "代理密码":"35B7E37DAF97"
        },
        # 测试配置
        "日常拖把开关": True,
        "日常魔化开关": True,
        "日常如意开关": True,
        "闲时任务": "闲时_禅壹"
    }

    print('开始测试...')
    myj = MYJ(config)
    time.sleep(5)

    # 运行主函数
    myj.主函数()

    del myj
    
if __name__ == '__main__':
    function_test()
    