import re
s = """
163546
<DIV id=npcList class=smallFont style="BORDER-TOP: #58b1d8 1px solid; HEIGHT: 180px; BORDER-RIGHT: #58b1d8 1px solid; WIDTH: 172px; OVERFLOW-Y: auto; BORDER-BOTTOM: #58b1d8 1px solid; POSITION: absolute; PADDING-BOTTOM: 5px; PADDING-TOP: 5px; PADDING-LEFT: 10px; LEFT: 450px; BORDER-LEFT: #58b1d8 1px solid; Z-INDEX: 10; TOP: 70px; PADDING-RIGHT: 10px; VISIBILITY: visible; BACKGROUND-COLOR: #c2e1eb"><DT id=npc_0><A onclick="return false;" class=black hideFocus href="http://x12.pet.imop.com/petwin.html#"><SPAN onclick="p.cmd('talk 0');p.hideWelcome();p.setFuJi('0');return false;" title=交谈>长眉老人<IMG border=0 src="http://x12.pet.imop.com/img/blank.gif" width=5 height=11></SPAN><A onclick="p.cmd('talk 0');event.cancelBubble=true;p.hideWelcome();return false;" title=交谈 href="http://x12.pet.imop.com/petwin.html#"><IMG border=0 src="http://x12.pet.imop.com/img/button/t.gif"><IMG border=0 src="http://x12.pet.imop.com/img/blank.gif" width=3 height=11></A></A></DT>
<DT id=npc_1><A onclick="return false;" class=black hideFocus href="http://x12.pet.imop.com/petwin.html#"><SPAN onclick="p.cmd('talk 1');p.hideWelcome();p.setFuJi('1');return false;" title=交谈>伤感的骚客<IMG border=0 src="http://x12.pet.imop.com/img/blank.gif" width=5 height=11></SPAN><A onclick="p.cmd('talk 1');event.cancelBubble=true;p.hideWelcome();return false;" title=交谈 href="http://x12.pet.imop.com/petwin.html#"><IMG border=0 src="http://x12.pet.imop.com/img/button/t.gif"><IMG border=0 src="http://x12.pet.imop.com/img/blank.gif" width=3 height=11></A></A></DT>
<DT id=npc_2><A onclick="return false;" class=black hideFocus href="http://x12.pet.imop.com/petwin.html#"><SPAN onclick="p.cmd('talk 2');p.hideWelcome();p.setFuJi('2');return false;" title=交谈>嫦娥姐姐<IMG border=0 src="http://x12.pet.imop.com/img/blank.gif" width=5 height=11></SPAN><A onclick="p.cmd('talk 2');event.cancelBubble=true;p.hideWelcome();return false;" title=交谈 href="http://x12.pet.imop.com/petwin.html#"><IMG border=0 src="http://x12.pet.imop.com/img/button/t.gif"><IMG border=0 src="http://x12.pet.imop.com/img/blank.gif" width=3 height=11></A></A></DT>
<DT id=npc_3><A onclick="return false;" class=black hideFocus href="http://x12.pet.imop.com/petwin.html#"><SPAN onclick="p.showPetInfo(3,'蜜蜂',15972944918857,[false,false,false,true]);p.setFuJi('3');return false;" title=查看>蜜蜂<IMG border=0 src="http://x12.pet.imop.com/img/blank.gif" width=5 height=11></SPAN><A onclick="p.cmd('45sr34 3 15972944918857');event.cancelBubble=true;return false;" title=攻击 style="DISPLAY: none" href="http://x12.pet.imop.com/petwin.html#"><IMG border=0 src="http://x12.pet.imop.com/img/button/a.gif"><IMG border=0 src="http://x12.pet.imop.com/img/blank.gif" width=3 height=11></A><A onclick="p.cmd('bar34 3 15972944918857');event.cancelBubble=true;return false;" title=攻击 href="http://x12.pet.imop.com/petwin.html#"><IMG border=0 src="http://x12.pet.imop.com/img/button/a.gif"><IMG border=0 src="http://x12.pet.imop.com/img/blank.gif" width=3 height=11></A></A></DT>
<DT id=npc_4><A onclick="return false;" class=black hideFocus href="http://x12.pet.imop.com/petwin.html#"><SPAN onclick="p.showPetInfo(4,'豆芽君',16785242160505,[false,false,false,true]);p.setFuJi('4');return false;" title=查看>豆芽君<IMG border=0 src="http://x12.pet.imop.com/img/blank.gif" width=5 height=11></SPAN><A onclick="p.cmd('45sr34 4 16785242160505');event.cancelBubble=true;return false;" title=攻击 style="DISPLAY: none" href="http://x12.pet.imop.com/petwin.html#"><IMG border=0 src="http://x12.pet.imop.com/img/button/a.gif"><IMG border=0 src="http://x12.pet.imop.com/img/blank.gif" width=3 height=11></A><A onclick="p.cmd('bar34 4 16785242160505');event.cancelBubble=true;return false;" title=攻击 href="http://x12.pet.imop.com/petwin.html#"><IMG border=0 src="http://x12.pet.imop.com/img/button/a.gif"><IMG border=0 src="http://x12.pet.imop.com/img/blank.gif" width=3 height=11></A></A></DT>
<DT id=npc_5><A onclick="return false;" class=black hideFocus href="http://x12.pet.imop.com/petwin.html#"><SPAN onclick="p.showPetInfo(5,'蚱蜢',22241072063311,[false,false,false,true]);p.setFuJi('5');return false;" title=查看>蚱蜢<IMG border=0 src="http://x12.pet.imop.com/img/blank.gif" width=5 height=11></SPAN><A onclick="p.cmd('45sr34 5 22241072063311');event.cancelBubble=true;return false;" title=攻击 style="DISPLAY: none" href="http://x12.pet.imop.com/petwin.html#"><IMG border=0 src="http://x12.pet.imop.com/img/button/a.gif"><IMG border=0 src="http://x12.pet.imop.com/img/blank.gif" width=3 height=11></A><A onclick="p.cmd('bar34 5 22241072063311');event.cancelBubble=true;return false;" title=攻击 href="http://x12.pet.imop.com/petwin.html#"><IMG border=0 src="http://x12.pet.imop.com/img/button/a.gif"><IMG border=0 src="http://x12.pet.imop.com/img/blank.gif" width=3 height=11></A></A></DT>
<DT id=npc_6><A onclick="return false;" class=black hideFocus href="http://x12.pet.imop.com/petwin.html#"><SPAN onclick="p.showPetInfo(6,'巨钳螃蟹',20674414915684,[false,false,false,true]);p.setFuJi('6');return false;" title=查看>巨钳螃蟹<IMG border=0 src="http://x12.pet.imop.com/img/blank.gif" width=5 height=11></SPAN><A onclick="p.cmd('45sr34 6 20674414915684');event.cancelBubble=true;return false;" title=攻击 style="DISPLAY: none" href="http://x12.pet.imop.com/petwin.html#"><IMG border=0 src="http://x12.pet.imop.com/img/button/a.gif"><IMG border=0 src="http://x12.pet.imop.com/img/blank.gif" width=3 height=11></A><A onclick="p.cmd('bar34 6 20674414915684');event.cancelBubble=true;return false;" title=攻击 href="http://x12.pet.imop.com/petwin.html#"><IMG border=0 src="http://x12.pet.imop.com/img/button/a.gif"><IMG border=0 src="http://x12.pet.imop.com/img/blank.gif" width=3 height=11></A></A></DT>
<DT id=npc_7><A onclick="return false;" class=black hideFocus href="http://x12.pet.imop.com/petwin.html#"><SPAN onclick="p.showPetInfo(7,'豆芽君',24823019760082,[false,false,false,true]);p.setFuJi('7');return false;" title=查看>豆芽君<IMG border=0 src="http://x12.pet.imop.com/img/blank.gif" width=5 height=11></SPAN><A onclick="p.cmd('45sr34 7 24823019760082');event.cancelBubble=true;return false;" title=攻击 style="DISPLAY: none" href="http://x12.pet.imop.com/petwin.html#"><IMG border=0 src="http://x12.pet.imop.com/img/button/a.gif"><IMG border=0 src="http://x12.pet.imop.com/img/blank.gif" width=3 height=11></A><A onclick="p.cmd('bar34 7 24823019760082');event.cancelBubble=true;return false;" title=攻击 href="http://x12.pet.imop.com/petwin.html#"><IMG border=0 src="http://x12.pet.imop.com/img/button/a.gif"><IMG border=0 src="http://x12.pet.imop.com/img/blank.gif" width=3 height=11></A></A></DT>
<DT id=npc_8><A onclick="return false;" class=black hideFocus href="http://x12.pet.imop.com/petwin.html#"><SPAN onclick="p.showPetInfo(8,'豆芽君',27196040685760,[false,false,false,true]);p.setFuJi('8');return false;" title=查看>豆芽君<IMG border=0 src="http://x12.pet.imop.com/img/blank.gif" width=5 height=11></SPAN><A onclick="p.cmd('45sr34 8 27196040685760');event.cancelBubble=true;return false;" title=攻击 style="DISPLAY: none" href="http://x12.pet.imop.com/petwin.html#"><IMG border=0 src="http://x12.pet.imop.com/img/button/a.gif"><IMG border=0 src="http://x12.pet.imop.com/img/blank.gif" width=3 height=11></A><A onclick="p.cmd('bar34 8 27196040685760');event.cancelBubble=true;return false;" title=攻击 href="http://x12.pet.imop.com/petwin.html#"><IMG border=0 src="http://x12.pet.imop.com/img/button/a.gif"><IMG border=0 src="http://x12.pet.imop.com/img/blank.gif" width=3 height=11></A></A></DT>
<DT id=npc_9><A onclick="return false;" class=black hideFocus href="http://x12.pet.imop.com/petwin.html#"><SPAN onclick="p.showPetInfo(9,'豆芽君',27344600033704,[false,false,false,true]);p.setFuJi('9');return false;" title=查看>豆芽君<IMG border=0 src="http://x12.pet.imop.com/img/blank.gif" width=5 height=11></SPAN><A onclick="p.cmd('45sr34 9 27344600033704');event.cancelBubble=true;return false;" title=攻击 style="DISPLAY: none" href="http://x12.pet.imop.com/petwin.html#"><IMG border=0 src="http://x12.pet.imop.com/img/button/a.gif"><IMG border=0 src="http://x12.pet.imop.com/img/blank.gif" width=3 height=11></A><A onclick="p.cmd('bar34 9 27344600033704');event.cancelBubble=true;return false;" title=攻击 href="http://x12.pet.imop.com/petwin.html#"><IMG border=0 src="http://x12.pet.imop.com/img/button/a.gif"><IMG border=0 src="http://x12.pet.imop.com/img/blank.gif" width=3 height=11></A></A></DT>
<DT id=npc_10><A onclick="return false;" class=black hideFocus href="http://x12.pet.imop.com/petwin.html#"><SPAN onclick="p.showPetInfo(10,'蜜蜂',27505147813216,[false,false,false,true]);p.setFuJi('10');return false;" title=查看>蜜蜂<IMG border=0 src="http://x12.pet.imop.com/img/blank.gif" width=5 height=11></SPAN><A onclick="p.cmd('45sr34 10 27505147813216');event.cancelBubble=true;return false;" title=攻击 style="DISPLAY: none" href="http://x12.pet.imop.com/petwin.html#"><IMG border=0 src="http://x12.pet.imop.com/img/button/a.gif"><IMG border=0 src="http://x12.pet.imop.com/img/blank.gif" width=3 height=11></A><A onclick="p.cmd('bar34 10 27505147813216');event.cancelBubble=true;return false;" title=攻击 href="http://x12.pet.imop.com/petwin.html#"><IMG border=0 src="http://x12.pet.imop.com/img/button/a.gif"><IMG border=0 src="http://x12.pet.imop.com/img/blank.gif" width=3 height=11></A></A></DT>
<DT id=npc_11><A onclick="return false;" class=black hideFocus href="http://x12.pet.imop.com/petwin.html#"><SPAN onclick="p.showPetInfo(11,'树蛙',27878105489209,[false,false,false,true]);p.setFuJi('11');return false;" title=查看>树蛙<IMG border=0 src="http://x12.pet.imop.com/img/blank.gif" width=5 height=11></SPAN><A onclick="p.cmd('45sr34 11 27878105489209');event.cancelBubble=true;return false;" title=攻击 style="DISPLAY: none" href="http://x12.pet.imop.com/petwin.html#"><IMG border=0 src="http://x12.pet.imop.com/img/button/a.gif"><IMG border=0 src="http://x12.pet.imop.com/img/blank.gif" width=3 height=11></A><A onclick="p.cmd('bar34 11 27878105489209');event.cancelBubble=true;return false;" title=攻击 href="http://x12.pet.imop.com/petwin.html#"><IMG border=0 src="http://x12.pet.imop.com/img/button/a.gif"><IMG border=0 src="http://x12.pet.imop.com/img/blank.gif" width=3 height=11></A></A></DT>
<DT id=npc_12><A onclick="return false;" class=black hideFocus href="http://x12.pet.imop.com/petwin.html#"><SPAN onclick="p.showPetInfo(12,'巨钳螃蟹',28007791157725,[false,false,false,true]);p.setFuJi('12');return false;" title=查看>巨钳螃蟹<IMG border=0 src="http://x12.pet.imop.com/img/blank.gif" width=5 height=11></SPAN><A onclick="p.cmd('45sr34 12 28007791157725');event.cancelBubble=true;return false;" title=攻击 style="DISPLAY: none" href="http://x12.pet.imop.com/petwin.html#"><IMG border=0 src="http://x12.pet.imop.com/img/button/a.gif"><IMG border=0 src="http://x12.pet.imop.com/img/blank.gif" width=3 height=11></A><A onclick="p.cmd('bar34 12 28007791157725');event.cancelBubble=true;return false;" title=攻击 href="http://x12.pet.imop.com/petwin.html#"><IMG border=0 src="http://x12.pet.imop.com/img/button/a.gif"><IMG border=0 src="http://x12.pet.imop.com/img/blank.gif" width=3 height=11></A></A></DT></DIV>


<DIV id=npcList class=smallFont style="BORDER-TOP: #58b1d8 1px solid; HEIGHT: 180px; BORDER-RIGHT: #58b1d8 1px solid; WIDTH: 172px; OVERFLOW-Y: auto; BORDER-BOTTOM: #58b1d8 1px solid; POSITION: absolute; PADDING-BOTTOM: 5px; PADDING-TOP: 5px; PADDING-LEFT: 10px; LEFT: 450px; BORDER-LEFT: #58b1d8 1px solid; Z-INDEX: 10; TOP: 70px; PADDING-RIGHT: 10px; VISIBILITY: visible; BACKGROUND-COLOR: #c2e1eb"><DT id=npc_0><A onclick="return false;" class=black hideFocus href="http://x12.pet.imop.com/petwin.html#"><SPAN onclick="p.cmd('talk 0');p.hideWelcome();p.setFuJi('0');return false;" title=交谈>长眉老人<IMG border=0 src="http://x12.pet.imop.com/img/blank.gif" width=5 height=11></SPAN><A onclick="p.cmd('talk 0');event.cancelBubble=true;p.hideWelcome();return false;" title=交谈 href="http://x12.pet.imop.com/petwin.html#"><IMG border=0 src="http://x12.pet.imop.com/img/button/t.gif"><IMG border=0 src="http://x12.pet.imop.com/img/blank.gif" width=3 height=11></A></A></DT>
<DT id=npc_1><A onclick="return false;" class=black hideFocus href="http://x12.pet.imop.com/petwin.html#"><SPAN onclick="p.cmd('talk 1');p.hideWelcome();p.setFuJi('1');return false;" title=交谈>伤感的骚客<IMG border=0 src="http://x12.pet.imop.com/img/blank.gif" width=5 height=11></SPAN><A onclick="p.cmd('talk 1');event.cancelBubble=true;p.hideWelcome();return false;" title=交谈 href="http://x12.pet.imop.com/petwin.html#"><IMG border=0 src="http://x12.pet.imop.com/img/button/t.gif"><IMG border=0 src="http://x12.pet.imop.com/img/blank.gif" width=3 height=11></A></A></DT>
<DT id=npc_2><A onclick="return false;" class=black hideFocus href="http://x12.pet.imop.com/petwin.html#"><SPAN onclick="p.cmd('talk 2');p.hideWelcome();p.setFuJi('2');return false;" title=交谈>嫦娥姐姐<IMG border=0 src="http://x12.pet.imop.com/img/blank.gif" width=5 height=11></SPAN><A onclick="p.cmd('talk 2');event.cancelBubble=true;p.hideWelcome();return false;" title=交谈 href="http://x12.pet.imop.com/petwin.html#"><IMG border=0 src="http://x12.pet.imop.com/img/button/t.gif"><IMG border=0 src="http://x12.pet.imop.com/img/blank.gif" width=3 height=11></A></A></DT>
<DT id=npc_3><A onclick="return false;" class=black hideFocus href="http://x12.pet.imop.com/petwin.html#"><SPAN onclick="p.showPetInfo(3,'蜜蜂',101417201,[false,false,false,true]);p.setFuJi('3');return false;" title=查看>蜜蜂<IMG border=0 src="http://x12.pet.imop.com/img/blank.gif" width=5 height=11></SPAN><A onclick="p.cmd('45sr34 3 101417201');event.cancelBubble=true;return false;" title=攻击 style="DISPLAY: none" href="http://x12.pet.imop.com/petwin.html#"><IMG border=0 src="http://x12.pet.imop.com/img/button/a.gif"><IMG border=0 src="http://x12.pet.imop.com/img/blank.gif" width=3 height=11></A><A onclick="p.cmd('bar34 3 101417201');event.cancelBubble=true;return false;" title=攻击 href="http://x12.pet.imop.com/petwin.html#"><IMG border=0 src="http://x12.pet.imop.com/img/button/a.gif"><IMG border=0 src="http://x12.pet.imop.com/img/blank.gif" width=3 height=11></A></A></DT>
<DT id=npc_4><A onclick="return false;" class=black hideFocus href="http://x12.pet.imop.com/petwin.html#"><SPAN onclick="p.showPetInfo(4,'树蛙',121667932,[false,false,false,true]);p.setFuJi('4');return false;" title=查看>树蛙<IMG border=0 src="http://x12.pet.imop.com/img/blank.gif" width=5 height=11></SPAN><A onclick="p.cmd('45sr34 4 121667932');event.cancelBubble=true;return false;" title=攻击 style="DISPLAY: none" href="http://x12.pet.imop.com/petwin.html#"><IMG border=0 src="http://x12.pet.imop.com/img/button/a.gif"><IMG border=0 src="http://x12.pet.imop.com/img/blank.gif" width=3 height=11></A><A onclick="p.cmd('bar34 4 121667932');event.cancelBubble=true;return false;" title=攻击 href="http://x12.pet.imop.com/petwin.html#"><IMG border=0 src="http://x12.pet.imop.com/img/button/a.gif"><IMG border=0 src="http://x12.pet.imop.com/img/blank.gif" width=3 height=11></A></A></DT>
<DT id=npc_5><A onclick="return false;" class=black hideFocus href="http://x12.pet.imop.com/petwin.html#"><SPAN onclick="p.showPetInfo(5,'豆芽君',141918663,[false,false,false,true]);p.setFuJi('5');return false;" title=查看>豆芽君<IMG border=0 src="http://x12.pet.imop.com/img/blank.gif" width=5 height=11></SPAN><A onclick="p.cmd('45sr34 5 141918663');event.cancelBubble=true;return false;" title=攻击 style="DISPLAY: none" href="http://x12.pet.imop.com/petwin.html#"><IMG border=0 src="http://x12.pet.imop.com/img/button/a.gif"><IMG border=0 src="http://x12.pet.imop.com/img/blank.gif" width=3 height=11></A><A onclick="p.cmd('bar34 5 141918663');event.cancelBubble=true;return false;" title=攻击 href="http://x12.pet.imop.com/petwin.html#"><IMG border=0 src="http://x12.pet.imop.com/img/button/a.gif"><IMG border=0 src="http://x12.pet.imop.com/img/blank.gif" width=3 height=11></A></A></DT>
<DT id=npc_6><A onclick="return false;" class=black hideFocus href="http://x12.pet.imop.com/petwin.html#"><SPAN onclick="p.showPetInfo(6,'豆芽君',162169394,[false,false,false,true]);p.setFuJi('6');return false;" title=查看>豆芽君<IMG border=0 src="http://x12.pet.imop.com/img/blank.gif" width=5 height=11></SPAN><A onclick="p.cmd('45sr34 6 162169394');event.cancelBubble=true;return false;" title=攻击 style="DISPLAY: none" href="http://x12.pet.imop.com/petwin.html#"><IMG border=0 src="http://x12.pet.imop.com/img/button/a.gif"><IMG border=0 src="http://x12.pet.imop.com/img/blank.gif" width=3 height=11></A><A onclick="p.cmd('bar34 6 162169394');event.cancelBubble=true;return false;" title=攻击 href="http://x12.pet.imop.com/petwin.html#"><IMG border=0 src="http://x12.pet.imop.com/img/button/a.gif"><IMG border=0 src="http://x12.pet.imop.com/img/blank.gif" width=3 height=11></A></A></DT>
<DT id=npc_7><A onclick="return false;" class=black hideFocus href="http://x12.pet.imop.com/petwin.html#"><SPAN onclick="p.showPetInfo(7,'蜜蜂',182420125,[false,false,false,true]);p.setFuJi('7');return false;" title=查看>蜜蜂<IMG border=0 src="http://x12.pet.imop.com/img/blank.gif" width=5 height=11></SPAN><A onclick="p.cmd('45sr34 7 182420125');event.cancelBubble=true;return false;" title=攻击 style="DISPLAY: none" href="http://x12.pet.imop.com/petwin.html#"><IMG border=0 src="http://x12.pet.imop.com/img/button/a.gif"><IMG border=0 src="http://x12.pet.imop.com/img/blank.gif" width=3 height=11></A><A onclick="p.cmd('bar34 7 182420125');event.cancelBubble=true;return false;" title=攻击 href="http://x12.pet.imop.com/petwin.html#"><IMG border=0 src="http://x12.pet.imop.com/img/button/a.gif"><IMG border=0 src="http://x12.pet.imop.com/img/blank.gif" width=3 height=11></A></A></DT>
<DT id=npc_8><A onclick="return false;" class=black hideFocus href="http://x12.pet.imop.com/petwin.html#"><SPAN onclick="p.showPetInfo(8,'蜜蜂',202670856,[false,false,false,true]);p.setFuJi('8');return false;" title=查看>蜜蜂<IMG border=0 src="http://x12.pet.imop.com/img/blank.gif" width=5 height=11></SPAN><A onclick="p.cmd('45sr34 8 202670856');event.cancelBubble=true;return false;" title=攻击 style="DISPLAY: none" href="http://x12.pet.imop.com/petwin.html#"><IMG border=0 src="http://x12.pet.imop.com/img/button/a.gif"><IMG border=0 src="http://x12.pet.imop.com/img/blank.gif" width=3 height=11></A><A onclick="p.cmd('bar34 8 202670856');event.cancelBubble=true;return false;" title=攻击 href="http://x12.pet.imop.com/petwin.html#"><IMG border=0 src="http://x12.pet.imop.com/img/button/a.gif"><IMG border=0 src="http://x12.pet.imop.com/img/blank.gif" width=3 height=11></A></A></DT>
<DT id=npc_9><A onclick="return false;" class=black hideFocus href="http://x12.pet.imop.com/petwin.html#"><SPAN onclick="p.showPetInfo(9,'树蛙',222921587,[false,false,false,true]);p.setFuJi('9');return false;" title=查看>树蛙<IMG border=0 src="http://x12.pet.imop.com/img/blank.gif" width=5 height=11></SPAN><A onclick="p.cmd('45sr34 9 222921587');event.cancelBubble=true;return false;" title=攻击 style="DISPLAY: none" href="http://x12.pet.imop.com/petwin.html#"><IMG border=0 src="http://x12.pet.imop.com/img/button/a.gif"><IMG border=0 src="http://x12.pet.imop.com/img/blank.gif" width=3 height=11></A><A onclick="p.cmd('bar34 9 222921587');event.cancelBubble=true;return false;" title=攻击 href="http://x12.pet.imop.com/petwin.html#"><IMG border=0 src="http://x12.pet.imop.com/img/button/a.gif"><IMG border=0 src="http://x12.pet.imop.com/img/blank.gif" width=3 height=11></A></A></DT>
<DT id=npc_10><A onclick="return false;" class=black hideFocus href="http://x12.pet.imop.com/petwin.html#"><SPAN onclick="p.showPetInfo(10,'豆芽君',243172318,[false,false,false,true]);p.setFuJi('10');return false;" title=查看>豆芽君<IMG border=0 src="http://x12.pet.imop.com/img/blank.gif" width=5 height=11></SPAN><A onclick="p.cmd('45sr34 10 243172318');event.cancelBubble=true;return false;" title=攻击 style="DISPLAY: none" href="http://x12.pet.imop.com/petwin.html#"><IMG border=0 src="http://x12.pet.imop.com/img/button/a.gif"><IMG border=0 src="http://x12.pet.imop.com/img/blank.gif" width=3 height=11></A><A onclick="p.cmd('bar34 10 243172318');event.cancelBubble=true;return false;" title=攻击 href="http://x12.pet.imop.com/petwin.html#"><IMG border=0 src="http://x12.pet.imop.com/img/button/a.gif"><IMG border=0 src="http://x12.pet.imop.com/img/blank.gif" width=3 height=11></A></A></DT>
<DT id=npc_11><A onclick="return false;" class=black hideFocus href="http://x12.pet.imop.com/petwin.html#"><SPAN onclick="p.showPetInfo(11,'豆芽君',263423049,[false,false,false,true]);p.setFuJi('11');return false;" title=查看>豆芽君<IMG border=0 src="http://x12.pet.imop.com/img/blank.gif" width=5 height=11></SPAN><A onclick="p.cmd('45sr34 11 263423049');event.cancelBubble=true;return false;" title=攻击 style="DISPLAY: none" href="http://x12.pet.imop.com/petwin.html#"><IMG border=0 src="http://x12.pet.imop.com/img/button/a.gif"><IMG border=0 src="http://x12.pet.imop.com/img/blank.gif" width=3 height=11></A><A onclick="p.cmd('bar34 11 263423049');event.cancelBubble=true;return false;" title=攻击 href="http://x12.pet.imop.com/petwin.html#"><IMG border=0 src="http://x12.pet.imop.com/img/button/a.gif"><IMG border=0 src="http://x12.pet.imop.com/img/blank.gif" width=3 height=11></A></A></DT>
<DT id=npc_12><A onclick="return false;" class=black hideFocus href="http://x12.pet.imop.com/petwin.html#"><SPAN onclick="p.showPetInfo(12,'巨钳螃蟹',283673780,[false,false,false,true]);p.setFuJi('12');return false;" title=查看>巨钳螃蟹<IMG border=0 src="http://x12.pet.imop.com/img/blank.gif" width=5 height=11></SPAN><A onclick="p.cmd('45sr34 12 283673780');event.cancelBubble=true;return false;" title=攻击 style="DISPLAY: none" href="http://x12.pet.imop.com/petwin.html#"><IMG border=0 src="http://x12.pet.imop.com/img/button/a.gif"><IMG border=0 src="http://x12.pet.imop.com/img/blank.gif" width=3 height=11></A><A onclick="p.cmd('bar34 12 283673780');event.cancelBubble=true;return false;" title=攻击 href="http://x12.pet.imop.com/petwin.html#"><IMG border=0 src="http://x12.pet.imop.com/img/button/a.gif"><IMG border=0 src="http://x12.pet.imop.com/img/blank.gif" width=3 height=11></A></A></DT>
<DT id=npc_13><A onclick="return false;" class=black hideFocus href="http://x12.pet.imop.com/petwin.html#"><SPAN onclick="p.showPetInfo(13,'豆芽君',513558701706,[false,false,false,true]);p.setFuJi('13');return false;" title=查看>豆芽君<IMG border=0 src="http://x12.pet.imop.com/img/blank.gif" width=5 height=11></SPAN><A onclick="p.cmd('45sr34 13 513558701706');event.cancelBubble=true;return false;" title=攻击 style="DISPLAY: none" href="http://x12.pet.imop.com/petwin.html#"><IMG border=0 src="http://x12.pet.imop.com/img/button/a.gif"><IMG border=0 src="http://x12.pet.imop.com/img/blank.gif" width=3 height=11></A><A onclick="p.cmd('bar34 13 513558701706');event.cancelBubble=true;return false;" title=攻击 href="http://x12.pet.imop.com/petwin.html#"><IMG border=0 src="http://x12.pet.imop.com/img/button/a.gif"><IMG border=0 src="http://x12.pet.imop.com/img/blank.gif" width=3 height=11></A></A></DT></DIV>

<DIV id=npcList class=smallFont style="BORDER-TOP: #58b1d8 1px solid; HEIGHT: 180px; BORDER-RIGHT: #58b1d8 1px solid; WIDTH: 172px; OVERFLOW-Y: auto; BORDER-BOTTOM: #58b1d8 1px solid; POSITION: absolute; PADDING-BOTTOM: 5px; PADDING-TOP: 5px; PADDING-LEFT: 10px; LEFT: 450px; BORDER-LEFT: #58b1d8 1px solid; Z-INDEX: 10; TOP: 70px; PADDING-RIGHT: 10px; VISIBILITY: visible; BACKGROUND-COLOR: #c2e1eb"><DT id=npc_0><A onclick="return false;" class=black hideFocus href="http://x12.pet.imop.com/petwin.html#"><SPAN onclick="p.showPetInfo(0,'蚱蜢',754582902068,[false,false,false,true]);p.setFuJi('0');return false;" title=查看>蚱蜢<IMG border=0 src="http://x12.pet.imop.com/img/blank.gif" width=5 height=11></SPAN><A onclick="p.cmd('45sr34 0 754582902068');event.cancelBubble=true;return false;" title=攻击 style="DISPLAY: none" href="http://x12.pet.imop.com/petwin.html#"><IMG border=0 src="http://x12.pet.imop.com/img/button/a.gif"><IMG border=0 src="http://x12.pet.imop.com/img/blank.gif" width=3 height=11></A><A onclick="p.cmd('bar34 0 754582902068');event.cancelBubble=true;return false;" title=攻击 href="http://x12.pet.imop.com/petwin.html#"><IMG border=0 src="http://x12.pet.imop.com/img/button/a.gif"><IMG border=0 src="http://x12.pet.imop.com/img/blank.gif" width=3 height=11></A></A></DT>
<DT id=npc_1><A onclick="return false;" class=black hideFocus href="http://x12.pet.imop.com/petwin.html#"><SPAN onclick="p.showPetInfo(1,'蚱蜢',754603152799,[false,false,false,true]);p.setFuJi('1');return false;" title=查看>蚱蜢<IMG border=0 src="http://x12.pet.imop.com/img/blank.gif" width=5 height=11></SPAN><A onclick="p.cmd('45sr34 1 754603152799');event.cancelBubble=true;return false;" title=攻击 style="DISPLAY: none" href="http://x12.pet.imop.com/petwin.html#"><IMG border=0 src="http://x12.pet.imop.com/img/button/a.gif"><IMG border=0 src="http://x12.pet.imop.com/img/blank.gif" width=3 height=11></A><A onclick="p.cmd('bar34 1 754603152799');event.cancelBubble=true;return false;" title=攻击 href="http://x12.pet.imop.com/petwin.html#"><IMG border=0 src="http://x12.pet.imop.com/img/button/a.gif"><IMG border=0 src="http://x12.pet.imop.com/img/blank.gif" width=3 height=11></A></A></DT>
<DT id=npc_2><A onclick="return false;" class=black hideFocus href="http://x12.pet.imop.com/petwin.html#"><SPAN onclick="p.showPetInfo(2,'豆芽君',754623403530,[false,false,false,true]);p.setFuJi('2');return false;" title=查看>豆芽君<IMG border=0 src="http://x12.pet.imop.com/img/blank.gif" width=5 height=11></SPAN><A onclick="p.cmd('45sr34 2 754623403530');event.cancelBubble=true;return false;" title=攻击 style="DISPLAY: none" href="http://x12.pet.imop.com/petwin.html#"><IMG border=0 src="http://x12.pet.imop.com/img/button/a.gif"><IMG border=0 src="http://x12.pet.imop.com/img/blank.gif" width=3 height=11></A><A onclick="p.cmd('bar34 2 754623403530');event.cancelBubble=true;return false;" title=攻击 href="http://x12.pet.imop.com/petwin.html#"><IMG border=0 src="http://x12.pet.imop.com/img/button/a.gif"><IMG border=0 src="http://x12.pet.imop.com/img/blank.gif" width=3 height=11></A></A></DT>
<DT id=npc_3><A onclick="return false;" class=black hideFocus href="http://x12.pet.imop.com/petwin.html#"><SPAN onclick="p.showPetInfo(3,'豆芽君',754643654261,[false,false,false,true]);p.setFuJi('3');return false;" title=查看>豆芽君<IMG border=0 src="http://x12.pet.imop.com/img/blank.gif" width=5 height=11></SPAN><A onclick="p.cmd('45sr34 3 754643654261');event.cancelBubble=true;return false;" title=攻击 style="DISPLAY: none" href="http://x12.pet.imop.com/petwin.html#"><IMG border=0 src="http://x12.pet.imop.com/img/button/a.gif"><IMG border=0 src="http://x12.pet.imop.com/img/blank.gif" width=3 height=11></A><A onclick="p.cmd('bar34 3 754643654261');event.cancelBubble=true;return false;" title=攻击 href="http://x12.pet.imop.com/petwin.html#"><IMG border=0 src="http://x12.pet.imop.com/img/button/a.gif"><IMG border=0 src="http://x12.pet.imop.com/img/blank.gif" width=3 height=11></A></A></DT>
<DT id=npc_4><A onclick="return false;" class=black hideFocus href="http://x12.pet.imop.com/petwin.html#"><SPAN onclick="p.showPetInfo(4,'蚱蜢',754663904992,[false,false,false,true]);p.setFuJi('4');return false;" title=查看>蚱蜢<IMG border=0 src="http://x12.pet.imop.com/img/blank.gif" width=5 height=11></SPAN><A onclick="p.cmd('45sr34 4 754663904992');event.cancelBubble=true;return false;" title=攻击 style="DISPLAY: none" href="http://x12.pet.imop.com/petwin.html#"><IMG border=0 src="http://x12.pet.imop.com/img/button/a.gif"><IMG border=0 src="http://x12.pet.imop.com/img/blank.gif" width=3 height=11></A><A onclick="p.cmd('bar34 4 754663904992');event.cancelBubble=true;return false;" title=攻击 href="http://x12.pet.imop.com/petwin.html#"><IMG border=0 src="http://x12.pet.imop.com/img/button/a.gif"><IMG border=0 src="http://x12.pet.imop.com/img/blank.gif" width=3 height=11></A></A></DT>
<DT id=npc_5><A onclick="return false;" class=black hideFocus href="http://x12.pet.imop.com/petwin.html#"><SPAN onclick="p.showPetInfo(5,'巨钳螃蟹',754684155723,[false,false,false,true]);p.setFuJi('5');return false;" title=查看>巨钳螃蟹<IMG border=0 src="http://x12.pet.imop.com/img/blank.gif" width=5 height=11></SPAN><A onclick="p.cmd('45sr34 5 754684155723');event.cancelBubble=true;return false;" title=攻击 style="DISPLAY: none" href="http://x12.pet.imop.com/petwin.html#"><IMG border=0 src="http://x12.pet.imop.com/img/button/a.gif"><IMG border=0 src="http://x12.pet.imop.com/img/blank.gif" width=3 height=11></A><A onclick="p.cmd('bar34 5 754684155723');event.cancelBubble=true;return false;" title=攻击 href="http://x12.pet.imop.com/petwin.html#"><IMG border=0 src="http://x12.pet.imop.com/img/button/a.gif"><IMG border=0 src="http://x12.pet.imop.com/img/blank.gif" width=3 height=11></A></A></DT>
<DT id=npc_6><A onclick="return false;" class=black hideFocus href="http://x12.pet.imop.com/petwin.html#"><SPAN onclick="p.showPetInfo(6,'蜜蜂',754704406454,[false,false,false,true]);p.setFuJi('6');return false;" title=查看>蜜蜂<IMG border=0 src="http://x12.pet.imop.com/img/blank.gif" width=5 height=11></SPAN><A onclick="p.cmd('45sr34 6 754704406454');event.cancelBubble=true;return false;" title=攻击 style="DISPLAY: none" href="http://x12.pet.imop.com/petwin.html#"><IMG border=0 src="http://x12.pet.imop.com/img/button/a.gif"><IMG border=0 src="http://x12.pet.imop.com/img/blank.gif" width=3 height=11></A><A onclick="p.cmd('bar34 6 754704406454');event.cancelBubble=true;return false;" title=攻击 href="http://x12.pet.imop.com/petwin.html#"><IMG border=0 src="http://x12.pet.imop.com/img/button/a.gif"><IMG border=0 src="http://x12.pet.imop.com/img/blank.gif" width=3 height=11></A></A></DT>
<DT id=npc_7><A onclick="return false;" class=black hideFocus href="http://x12.pet.imop.com/petwin.html#"><SPAN onclick="p.showPetInfo(7,'蚱蜢',754724657185,[false,false,false,true]);p.setFuJi('7');return false;" title=查看>蚱蜢<IMG border=0 src="http://x12.pet.imop.com/img/blank.gif" width=5 height=11></SPAN><A onclick="p.cmd('45sr34 7 754724657185');event.cancelBubble=true;return false;" title=攻击 style="DISPLAY: none" href="http://x12.pet.imop.com/petwin.html#"><IMG border=0 src="http://x12.pet.imop.com/img/button/a.gif"><IMG border=0 src="http://x12.pet.imop.com/img/blank.gif" width=3 height=11></A><A onclick="p.cmd('bar34 7 754724657185');event.cancelBubble=true;return false;" title=攻击 href="http://x12.pet.imop.com/petwin.html#"><IMG border=0 src="http://x12.pet.imop.com/img/button/a.gif"><IMG border=0 src="http://x12.pet.imop.com/img/blank.gif" width=3 height=11></A></A></DT>
<DT id=npc_8><A onclick="return false;" class=black hideFocus href="http://x12.pet.imop.com/petwin.html#"><SPAN onclick="p.showPetInfo(8,'豆芽君',754744907916,[false,false,false,true]);p.setFuJi('8');return false;" title=查看>豆芽君<IMG border=0 src="http://x12.pet.imop.com/img/blank.gif" width=5 height=11></SPAN><A onclick="p.cmd('45sr34 8 754744907916');event.cancelBubble=true;return false;" title=攻击 style="DISPLAY: none" href="http://x12.pet.imop.com/petwin.html#"><IMG border=0 src="http://x12.pet.imop.com/img/button/a.gif"><IMG border=0 src="http://x12.pet.imop.com/img/blank.gif" width=3 height=11></A><A onclick="p.cmd('bar34 8 754744907916');event.cancelBubble=true;return false;" title=攻击 href="http://x12.pet.imop.com/petwin.html#"><IMG border=0 src="http://x12.pet.imop.com/img/button/a.gif"><IMG border=0 src="http://x12.pet.imop.com/img/blank.gif" width=3 height=11></A></A></DT>
<DT id=npc_9><A onclick="return false;" class=black hideFocus href="http://x12.pet.imop.com/petwin.html#"><SPAN onclick="p.showPetInfo(9,'蚱蜢',754765158647,[false,false,false,true]);p.setFuJi('9');return false;" title=查看>蚱蜢<IMG border=0 src="http://x12.pet.imop.com/img/blank.gif" width=5 height=11></SPAN><A onclick="p.cmd('45sr34 9 754765158647');event.cancelBubble=true;return false;" title=攻击 style="DISPLAY: none" href="http://x12.pet.imop.com/petwin.html#"><IMG border=0 src="http://x12.pet.imop.com/img/button/a.gif"><IMG border=0 src="http://x12.pet.imop.com/img/blank.gif" width=3 height=11></A><A onclick="p.cmd('bar34 9 754765158647');event.cancelBubble=true;return false;" title=攻击 href="http://x12.pet.imop.com/petwin.html#"><IMG border=0 src="http://x12.pet.imop.com/img/button/a.gif"><IMG border=0 src="http://x12.pet.imop.com/img/blank.gif" width=3 height=11></A></A></DT></DIV>



<DIV id=npcList class=smallFont style="BORDER-TOP: #58b1d8 1px solid; HEIGHT: 180px; BORDER-RIGHT: #58b1d8 1px solid; WIDTH: 172px; OVERFLOW-Y: auto; BORDER-BOTTOM: #58b1d8 1px solid; POSITION: absolute; PADDING-BOTTOM: 5px; PADDING-TOP: 5px; PADDING-LEFT: 10px; LEFT: 450px; BORDER-LEFT: #58b1d8 1px solid; Z-INDEX: 10; TOP: 70px; PADDING-RIGHT: 10px; VISIBILITY: visible; BACKGROUND-COLOR: #c2e1eb"><DT id=npc_0><A onclick="return false;" class=black hideFocus href="http://x12.pet.imop.com/petwin.html#"><SPAN onclick="p.showPetInfo(0,'树蛙',754177887448,[false,false,false,true]);p.setFuJi('0');return false;" title=查看>树蛙<IMG border=0 src="http://x12.pet.imop.com/img/blank.gif" width=5 height=11></SPAN><A onclick="p.cmd('45sr34 0 754177887448');event.cancelBubble=true;return false;" title=攻击 style="DISPLAY: none" href="http://x12.pet.imop.com/petwin.html#"><IMG border=0 src="http://x12.pet.imop.com/img/button/a.gif"><IMG border=0 src="http://x12.pet.imop.com/img/blank.gif" width=3 height=11></A><A onclick="p.cmd('bar34 0 754177887448');event.cancelBubble=true;return false;" title=攻击 href="http://x12.pet.imop.com/petwin.html#"><IMG border=0 src="http://x12.pet.imop.com/img/button/a.gif"><IMG border=0 src="http://x12.pet.imop.com/img/blank.gif" width=3 height=11></A></A></DT>
<DT id=npc_1><A onclick="return false;" class=black hideFocus href="http://x12.pet.imop.com/petwin.html#"><SPAN onclick="p.showPetInfo(1,'树蛙',754198138179,[false,false,false,true]);p.setFuJi('1');return false;" title=查看>树蛙<IMG border=0 src="http://x12.pet.imop.com/img/blank.gif" width=5 height=11></SPAN><A onclick="p.cmd('45sr34 1 754198138179');event.cancelBubble=true;return false;" title=攻击 style="DISPLAY: none" href="http://x12.pet.imop.com/petwin.html#"><IMG border=0 src="http://x12.pet.imop.com/img/button/a.gif"><IMG border=0 src="http://x12.pet.imop.com/img/blank.gif" width=3 height=11></A><A onclick="p.cmd('bar34 1 754198138179');event.cancelBubble=true;return false;" title=攻击 href="http://x12.pet.imop.com/petwin.html#"><IMG border=0 src="http://x12.pet.imop.com/img/button/a.gif"><IMG border=0 src="http://x12.pet.imop.com/img/blank.gif" width=3 height=11></A></A></DT>
<DT id=npc_2><A onclick="return false;" class=black hideFocus href="http://x12.pet.imop.com/petwin.html#"><SPAN onclick="p.showPetInfo(2,'蚱蜢',754218388910,[false,false,false,true]);p.setFuJi('2');return false;" title=查看>蚱蜢<IMG border=0 src="http://x12.pet.imop.com/img/blank.gif" width=5 height=11></SPAN><A onclick="p.cmd('45sr34 2 754218388910');event.cancelBubble=true;return false;" title=攻击 style="DISPLAY: none" href="http://x12.pet.imop.com/petwin.html#"><IMG border=0 src="http://x12.pet.imop.com/img/button/a.gif"><IMG border=0 src="http://x12.pet.imop.com/img/blank.gif" width=3 height=11></A><A onclick="p.cmd('bar34 2 754218388910');event.cancelBubble=true;return false;" title=攻击 href="http://x12.pet.imop.com/petwin.html#"><IMG border=0 src="http://x12.pet.imop.com/img/button/a.gif"><IMG border=0 src="http://x12.pet.imop.com/img/blank.gif" width=3 height=11></A></A></DT>
<DT id=npc_3><A onclick="return false;" class=black hideFocus href="http://x12.pet.imop.com/petwin.html#"><SPAN onclick="p.showPetInfo(3,'蜜蜂',754238639641,[false,false,false,true]);p.setFuJi('3');return false;" title=查看>蜜蜂<IMG border=0 src="http://x12.pet.imop.com/img/blank.gif" width=5 height=11></SPAN><A onclick="p.cmd('45sr34 3 754238639641');event.cancelBubble=true;return false;" title=攻击 style="DISPLAY: none" href="http://x12.pet.imop.com/petwin.html#"><IMG border=0 src="http://x12.pet.imop.com/img/button/a.gif"><IMG border=0 src="http://x12.pet.imop.com/img/blank.gif" width=3 height=11></A><A onclick="p.cmd('bar34 3 754238639641');event.cancelBubble=true;return false;" title=攻击 href="http://x12.pet.imop.com/petwin.html#"><IMG border=0 src="http://x12.pet.imop.com/img/button/a.gif"><IMG border=0 src="http://x12.pet.imop.com/img/blank.gif" width=3 height=11></A></A></DT>
<DT id=npc_4><A onclick="return false;" class=black hideFocus href="http://x12.pet.imop.com/petwin.html#"><SPAN onclick="p.showPetInfo(4,'蜜蜂',754258890372,[false,false,false,true]);p.setFuJi('4');return false;" title=查看>蜜蜂<IMG border=0 src="http://x12.pet.imop.com/img/blank.gif" width=5 height=11></SPAN><A onclick="p.cmd('45sr34 4 754258890372');event.cancelBubble=true;return false;" title=攻击 style="DISPLAY: none" href="http://x12.pet.imop.com/petwin.html#"><IMG border=0 src="http://x12.pet.imop.com/img/button/a.gif"><IMG border=0 src="http://x12.pet.imop.com/img/blank.gif" width=3 height=11></A><A onclick="p.cmd('bar34 4 754258890372');event.cancelBubble=true;return false;" title=攻击 href="http://x12.pet.imop.com/petwin.html#"><IMG border=0 src="http://x12.pet.imop.com/img/button/a.gif"><IMG border=0 src="http://x12.pet.imop.com/img/blank.gif" width=3 height=11></A></A></DT>
<DT id=npc_5><A onclick="return false;" class=black hideFocus href="http://x12.pet.imop.com/petwin.html#"><SPAN onclick="p.showPetInfo(5,'蚱蜢',754279141103,[false,false,false,true]);p.setFuJi('5');return false;" title=查看>蚱蜢<IMG border=0 src="http://x12.pet.imop.com/img/blank.gif" width=5 height=11></SPAN><A onclick="p.cmd('45sr34 5 754279141103');event.cancelBubble=true;return false;" title=攻击 style="DISPLAY: none" href="http://x12.pet.imop.com/petwin.html#"><IMG border=0 src="http://x12.pet.imop.com/img/button/a.gif"><IMG border=0 src="http://x12.pet.imop.com/img/blank.gif" width=3 height=11></A><A onclick="p.cmd('bar34 5 754279141103');event.cancelBubble=true;return false;" title=攻击 href="http://x12.pet.imop.com/petwin.html#"><IMG border=0 src="http://x12.pet.imop.com/img/button/a.gif"><IMG border=0 src="http://x12.pet.imop.com/img/blank.gif" width=3 height=11></A></A></DT>
<DT id=npc_6><A onclick="return false;" class=black hideFocus href="http://x12.pet.imop.com/petwin.html#"><SPAN onclick="p.showPetInfo(6,'蚱蜢',754299391834,[false,false,false,true]);p.setFuJi('6');return false;" title=查看>蚱蜢<IMG border=0 src="http://x12.pet.imop.com/img/blank.gif" width=5 height=11></SPAN><A onclick="p.cmd('45sr34 6 754299391834');event.cancelBubble=true;return false;" title=攻击 style="DISPLAY: none" href="http://x12.pet.imop.com/petwin.html#"><IMG border=0 src="http://x12.pet.imop.com/img/button/a.gif"><IMG border=0 src="http://x12.pet.imop.com/img/blank.gif" width=3 height=11></A><A onclick="p.cmd('bar34 6 754299391834');event.cancelBubble=true;return false;" title=攻击 href="http://x12.pet.imop.com/petwin.html#"><IMG border=0 src="http://x12.pet.imop.com/img/button/a.gif"><IMG border=0 src="http://x12.pet.imop.com/img/blank.gif" width=3 height=11></A></A></DT>
<DT id=npc_7><A onclick="return false;" class=black hideFocus href="http://x12.pet.imop.com/petwin.html#"><SPAN onclick="p.showPetInfo(7,'巨钳螃蟹',754319642565,[false,false,false,true]);p.setFuJi('7');return false;" title=查看>巨钳螃蟹<IMG border=0 src="http://x12.pet.imop.com/img/blank.gif" width=5 height=11></SPAN><A onclick="p.cmd('45sr34 7 754319642565');event.cancelBubble=true;return false;" title=攻击 style="DISPLAY: none" href="http://x12.pet.imop.com/petwin.html#"><IMG border=0 src="http://x12.pet.imop.com/img/button/a.gif"><IMG border=0 src="http://x12.pet.imop.com/img/blank.gif" width=3 height=11></A><A onclick="p.cmd('bar34 7 754319642565');event.cancelBubble=true;return false;" title=攻击 href="http://x12.pet.imop.com/petwin.html#"><IMG border=0 src="http://x12.pet.imop.com/img/button/a.gif"><IMG border=0 src="http://x12.pet.imop.com/img/blank.gif" width=3 height=11></A></A></DT>
<DT id=npc_8><A onclick="return false;" class=black hideFocus href="http://x12.pet.imop.com/petwin.html#"><SPAN onclick="p.showPetInfo(8,'蜜蜂',754339893296,[false,false,false,true]);p.setFuJi('8');return false;" title=查看>蜜蜂<IMG border=0 src="http://x12.pet.imop.com/img/blank.gif" width=5 height=11></SPAN><A onclick="p.cmd('45sr34 8 754339893296');event.cancelBubble=true;return false;" title=攻击 style="DISPLAY: none" href="http://x12.pet.imop.com/petwin.html#"><IMG border=0 src="http://x12.pet.imop.com/img/button/a.gif"><IMG border=0 src="http://x12.pet.imop.com/img/blank.gif" width=3 height=11></A><A onclick="p.cmd('bar34 8 754339893296');event.cancelBubble=true;return false;" title=攻击 href="http://x12.pet.imop.com/petwin.html#"><IMG border=0 src="http://x12.pet.imop.com/img/button/a.gif"><IMG border=0 src="http://x12.pet.imop.com/img/blank.gif" width=3 height=11></A></A></DT>
<DT id=npc_9><A onclick="return false;" class=black hideFocus href="http://x12.pet.imop.com/petwin.html#"><SPAN onclick="p.showPetInfo(9,'蚱蜢',754360144027,[false,false,false,true]);p.setFuJi('9');return false;" title=查看>蚱蜢<IMG border=0 src="http://x12.pet.imop.com/img/blank.gif" width=5 height=11></SPAN><A onclick="p.cmd('45sr34 9 754360144027');event.cancelBubble=true;return false;" title=攻击 style="DISPLAY: none" href="http://x12.pet.imop.com/petwin.html#"><IMG border=0 src="http://x12.pet.imop.com/img/button/a.gif"><IMG border=0 src="http://x12.pet.imop.com/img/blank.gif" width=3 height=11></A><A onclick="p.cmd('bar34 9 754360144027');event.cancelBubble=true;return false;" title=攻击 href="http://x12.pet.imop.com/petwin.html#"><IMG border=0 src="http://x12.pet.imop.com/img/button/a.gif"><IMG border=0 src="http://x12.pet.imop.com/img/blank.gif" width=3 height=11></A></A></DT></DIV>


"""

res = re.findall(r"p\.showPetInfo\((\d+),\'(.*?)\',(\d+),", s)
res = [(int(i[0]), i[1], int(i[2]) - 163546)for i in res]
for i in res:
    print(i)