# -*- coding: utf-8 -*-
import threading
import time
import ctypes
import sys
from enum import Enum


class ThreadState(Enum):
    """线程状态枚举"""
    CREATED = "created"      # 已创建
    RUNNING = "running"      # 运行中
    PAUSED = "paused"        # 已暂停
    STOPPED = "stopped"      # 已停止
    DESTROYED = "destroyed"  # 已销毁


class MyThread:
    """可控制的线程类，支持创建、运行、暂停、继续、销毁操作"""

    def __init__(self, target=None, args=(), kwargs=None, daemon=True, name=None):
        """
        初始化线程

        :param target: 目标函数
        :param args: 位置参数元组
        :param kwargs: 关键字参数字典
        :param daemon: 是否为守护线程
        :param name: 线程名称
        """
        self.target = target
        self.args = args
        self.kwargs = kwargs or {}
        self.daemon = daemon
        self.name = name

        # 线程状态
        self._state = ThreadState.CREATED
        self._thread = None
        self._lock = threading.Lock()
        self._thread_id = None

        # Windows API 函数
        if sys.platform == "win32":
            self._kernel32 = ctypes.windll.kernel32
            self._SuspendThread = self._kernel32.SuspendThread
            self._ResumeThread = self._kernel32.ResumeThread
            self._OpenThread = self._kernel32.OpenThread
            self._CloseHandle = self._kernel32.CloseHandle
            self._TerminateThread = self._kernel32.TerminateThread

            # 线程访问权限常量
            self.THREAD_SUSPEND_RESUME = 0x0002
            self.THREAD_TERMINATE = 0x0001
            self.THREAD_ALL_ACCESS = 0x1F03FF
    
    def _get_thread_id(self):
        """获取系统级线程ID"""
        if sys.platform == "win32":
            # 获取当前线程的系统ID
            return self._kernel32.GetCurrentThreadId()
        return None

    def _run_wrapper(self):
        """线程运行包装器"""
        try:
            # 保存系统级线程ID
            self._thread_id = self._get_thread_id()

            # 执行目标函数
            if self.target:
                self.target(*self.args, **self.kwargs)

        except Exception as e:
            print(f"线程 {self.name} 执行异常: {e}")
        finally:
            with self._lock:
                if self._state != ThreadState.DESTROYED:
                    self._state = ThreadState.STOPPED
    
    def start(self):
        """启动线程"""
        with self._lock:
            if self._state != ThreadState.CREATED:
                raise RuntimeError(f"线程状态错误: {self._state.value}")
            
            self._thread = threading.Thread(
                target=self._run_wrapper,
                daemon=self.daemon,
                name=self.name
            )
            self._thread.start()
            self._state = ThreadState.RUNNING
            return True
    
    def pause(self):
        """暂停线程"""
        with self._lock:
            if self._state == ThreadState.RUNNING and sys.platform == "win32" and self._thread_id:
                # 获取线程句柄
                thread_handle = self._OpenThread(self.THREAD_SUSPEND_RESUME, False, self._thread_id)
                if thread_handle:
                    # 暂停线程
                    result = self._SuspendThread(thread_handle)
                    self._CloseHandle(thread_handle)
                    if result != -1:  # -1 表示失败
                        self._state = ThreadState.PAUSED
                        return True
            return False
    
    def resume(self):
        """继续线程"""
        with self._lock:
            if self._state == ThreadState.PAUSED and sys.platform == "win32" and self._thread_id:
                # 获取线程句柄
                thread_handle = self._OpenThread(self.THREAD_SUSPEND_RESUME, False, self._thread_id)
                if thread_handle:
                    # 恢复线程
                    result = self._ResumeThread(thread_handle)
                    self._CloseHandle(thread_handle)
                    if result != -1:  # -1 表示失败
                        self._state = ThreadState.RUNNING
                        return True
            return False
    
    def stop(self):
        """停止线程"""
        with self._lock:
            if self._state in [ThreadState.RUNNING, ThreadState.PAUSED]:
                # 如果线程被暂停，先恢复它
                if self._state == ThreadState.PAUSED:
                    self.resume()
                self._state = ThreadState.STOPPED
                return True
            return False
    
    def destroy(self):
        """销毁线程"""
        with self._lock:
            if self._state == ThreadState.DESTROYED:
                return True

            # 如果线程被暂停，先恢复它
            if self._state == ThreadState.PAUSED:
                self.resume()

            # 强制终止线程（Windows）
            if sys.platform == "win32" and self._thread_id:
                thread_handle = self._OpenThread(self.THREAD_TERMINATE, False, self._thread_id)
                if thread_handle:
                    self._TerminateThread(thread_handle, 0)
                    self._CloseHandle(thread_handle)

            self._state = ThreadState.DESTROYED

            if self._thread and self._thread.is_alive():
                # 等待线程结束，最多等待2秒
                self._thread.join(timeout=2)

            return True
    
    def is_alive(self):
        """检查线程是否存活"""
        return self._thread and self._thread.is_alive()
    
    def get_state(self):
        """获取线程状态"""
        return self._state
    
    def join(self, timeout=None):
        """等待线程结束"""
        if self._thread:
            self._thread.join(timeout)
    
    def __str__(self):
        return f"ControllableThread(name={self.name}, state={self._state.value})"
    
    def __repr__(self):
        return self.__str__()


def my_loop_function():
    """示例循环函数"""
    n = 0
    while True:
        n += 1
        print(n)
        time.sleep(1)

def function_test():
    t = MyThread(target=my_loop_function, name="MyLoop")
    t.start()
    time.sleep(5)
    print('暂停线程5秒')
    t.pause()
    time.sleep(5)
    print('恢复线程')
    t.resume()
    time.sleep(5)
    print('销毁线程')
    t.destroy()

if __name__ == "__main__":
    function_test()
